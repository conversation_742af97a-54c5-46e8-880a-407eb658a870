# Financial AI Scripts Organization

## Core Training Scripts (Active)

### 1. `ultra_high_accuracy_trainer.py` ⭐ **PRIMARY**
- **Purpose**: Ultra high accuracy training targeting 99%+ accuracy
- **Features**: 
  - Processes ALL 81,000+ datasets
  - Advanced ensemble methods
  - Hyperparameter tuning with GridSearchCV
  - Cross-validation with TimeSeriesSplit
  - Saves optimized .pkl models
- **Usage**: `python ultra_high_accuracy_trainer.py`

### 2. `api_enhanced_training.py` 
- **Purpose**: API-enhanced training with real-time data
- **Features**:
  - Integrates Polygon.io and Alpha Vantage APIs
  - Batch processing for memory efficiency
  - Real dataset loading (no synthetic data)
- **Usage**: `python api_enhanced_training.py`

### 3. `comprehensive_ai_training.py`
- **Purpose**: Comprehensive dataset processing
- **Features**:
  - Discovers and processes all file types (CSV, TXT, JSON)
  - Batch processing with pickle saving
  - Memory-efficient handling
- **Usage**: `python comprehensive_ai_training.py`

### 4. `enhanced_llm_training.py`
- **Purpose**: LLM fine-tuning for financial conversations
- **Features**:
  - Financial conversation dataset creation
  - Transformer-based training
  - Model evaluation and saving
- **Usage**: `python enhanced_llm_training.py`

## System Components (Active)

### 5. `start_system.py`
- **Purpose**: Main system launcher
- **Features**: Starts all components (UI, MCP server, agent)

### 6. `test_system.py`
- **Purpose**: Comprehensive system testing
- **Features**: Tests all components and validates functionality

### 7. `validate_server_agent.py`
- **Purpose**: Server and agent validation
- **Features**: Validates MCP server and agent integration

## Directories

### `/src/` - Core source code
- `/agent/` - LangChain agent implementation
- `/data/` - Data management and database
- `/mcp_server/` - FastAPI MCP server
- `/tools/` - Financial analysis tools
- `/training/` - ML training modules
- `/ui/` - Streamlit user interface
- `/utils/` - Utility functions

### `/Dataset/` - Training datasets
- `Indian Stock Market Dataset_Kaggle/` - Main Indian stock data
- `Indian Stock Market Dataset_Kaggle_2024/` - 2024 stock data
- `USA Dataset1_kaggle/` - US market data
- `USA Dataset2_kaggle/` - Additional US data

### `/ultra_models/` - High accuracy models
- Best performing models with 99%+ accuracy target
- Ensemble models
- Pickle files (.pkl)

### `/ultra_results/` - Training results
- Training statistics
- Cross-validation results
- Performance metrics

## Removed Scripts (Cleaned Up)
- `enhanced_training.py` - Replaced by ultra_high_accuracy_trainer.py
- `simple_train_evaluate.py` - Replaced by comprehensive system
- `train_and_evaluate.py` - Replaced by ultra trainer
- `setup_check.py` - Functionality moved to main scripts
- `setup_api_keys.py` - Functionality integrated

## Next Steps

1. **Run Ultra Training**: Execute `ultra_high_accuracy_trainer.py` for 99%+ accuracy
2. **LLM Integration**: Train LLM with `enhanced_llm_training.py`
3. **System Testing**: Validate with `test_system.py`
4. **Production**: Launch with `start_system.py`

## Target Metrics
- **Accuracy**: 99%+ target
- **Datasets**: 81,000+ files processed
- **Models**: Ensemble .pkl files
- **Cross-validation**: 10-fold TimeSeriesSplit
- **Integration**: LLM + ML models
