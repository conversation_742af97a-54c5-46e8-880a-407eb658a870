"""
Advanced ML models for financial prediction with cross-validation and ensemble methods.
Implements multiple algorithms: XGBoost, LightGBM, CatBoost, LSTM, and ensemble models.
Enhanced with Polygon.io and Alpha Vantage APIs for real-time data.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from datetime import datetime, timedelta
import joblib
from pathlib import Path
import os
import requests
import time

# ML Libraries
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor
import xgboost as xgb
import lightgbm as lgb
import catboost as cb
from sklearn.linear_model import Ridge, Lasso
import optuna

# Technical Analysis
try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False

# API Libraries
try:
    from polygon import RESTClient
    POLYGON_AVAILABLE = True
except ImportError:
    POLYGON_AVAILABLE = False

try:
    from alpha_vantage.timeseries import TimeSeries
    from alpha_vantage.techindicators import TechIndicators
    ALPHA_VANTAGE_AVAILABLE = True
except ImportError:
    ALPHA_VANTAGE_AVAILABLE = False

# Database
from sqlalchemy.orm import Session
from src.data.database_schema import db_manager, Company, StockQuote, FinancialStatement, PricePrediction
from src.utils.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)

class AdvancedFinancialPredictor:
    """Advanced ML predictor with ensemble methods and cross-validation."""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.model_performance = {}
        self.models_dir = settings.PROJECT_ROOT / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # Initialize models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize all ML models."""
        self.models = {
            'xgboost': xgb.XGBRegressor(
                n_estimators=1000,
                max_depth=6,
                learning_rate=0.01,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                early_stopping_rounds=50
            ),
            'lightgbm': lgb.LGBMRegressor(
                n_estimators=1000,
                max_depth=6,
                learning_rate=0.01,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                verbose=-1
            ),
            'catboost': cb.CatBoostRegressor(
                iterations=1000,
                depth=6,
                learning_rate=0.01,
                random_seed=42,
                verbose=False
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=500,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            'ridge': Ridge(alpha=1.0),
            'lasso': Lasso(alpha=0.1)
        }
        
        self.scalers = {
            'standard': StandardScaler(),
            'robust': RobustScaler()
        }
    
    def prepare_features(self, company_id: int, lookback_days: int = 252) -> pd.DataFrame:
        """Prepare comprehensive feature set for ML models."""
        logger.info(f"Preparing features for company {company_id}")
        
        session = db_manager.get_session()
        
        try:
            # Get historical stock data
            quotes = session.query(StockQuote).filter(
                StockQuote.company_id == company_id
            ).order_by(StockQuote.timestamp.desc()).limit(lookback_days * 2).all()
            
            if len(quotes) < 50:
                logger.warning(f"Insufficient data for company {company_id}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame([{
                'timestamp': q.timestamp,
                'open': q.open_price,
                'high': q.high_price,
                'low': q.low_price,
                'close': q.current_price,
                'volume': q.volume or 0,
                'market_cap': q.market_cap_full or 0
            } for q in reversed(quotes)])
            
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # Technical indicators
            df = self._add_technical_indicators(df)
            
            # Price-based features
            df = self._add_price_features(df)
            
            # Volume features
            df = self._add_volume_features(df)
            
            # Volatility features
            df = self._add_volatility_features(df)
            
            # Fundamental features
            df = self._add_fundamental_features(df, company_id, session)
            
            # Market features
            df = self._add_market_features(df, session)
            
            # Time-based features
            df = self._add_time_features(df)
            
            # Remove NaN values
            df = df.dropna()
            
            logger.info(f"Prepared {len(df)} samples with {len(df.columns)} features")
            return df
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return pd.DataFrame()
        finally:
            session.close()
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add comprehensive technical indicators."""

        if not TA_AVAILABLE:
            # Fallback to basic indicators without ta library
            logger.warning("Using basic technical indicators (ta library not available)")

            # Simple moving averages
            for period in [5, 10, 20, 50, 100, 200]:
                df[f'sma_{period}'] = df['close'].roand get accuracy of 99lling(window=period).mean()
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()

            # Simple RSI calculation
            def calculate_rsi(prices, period=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                return 100 - (100 / (1 + rs))

            df['rsi_14'] = calculate_rsi(df['close'])

            return df

        # Use ta library if available
        try:
            # Moving averages
            for period in [5, 10, 20, 50, 100, 200]:
                df[f'sma_{period}'] = ta.trend.sma_indicator(df['close'], window=period)
                df[f'ema_{period}'] = ta.trend.ema_indicator(df['close'], window=period)

            # RSI
            for period in [14, 21, 30]:
                df[f'rsi_{period}'] = ta.momentum.rsi(df['close'], window=period)

            # MACD
            df['macd'] = ta.trend.macd(df['close'])
            df['macd_signal'] = ta.trend.macd_signal(df['close'])
            df['macd_diff'] = ta.trend.macd_diff(df['close'])

            # Bollinger Bands
            df['bollinger_upper'] = ta.volatility.bollinger_hband(df['close'])
            df['bollinger_lower'] = ta.volatility.bollinger_lband(df['close'])
            df['bollinger_middle'] = ta.volatility.bollinger_mavg(df['close'])

            # Stochastic
            df['stoch_k'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])

            # Williams %R
            df['williams_r'] = ta.momentum.williams_r(df['high'], df['low'], df['close'])

            # Average True Range
            df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])

            # Commodity Channel Index
            df['cci'] = ta.trend.cci(df['high'], df['low'], df['close'])

        except Exception as e:
            logger.warning(f"Error with ta indicators: {e}, using basic indicators")
            # Fallback to basic indicators
            for period in [5, 10, 20, 50]:
                df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()

        return df
    
    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price-based features."""
        
        # Returns
        for period in [1, 3, 5, 10, 20]:
            df[f'return_{period}d'] = df['close'].pct_change(period)
        
        # Price ratios
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Price position within day's range
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # Distance from moving averages
        for period in [20, 50, 200]:
            sma_col = f'sma_{period}'
            if sma_col in df.columns:
                df[f'price_vs_{sma_col}'] = (df['close'] - df[sma_col]) / df[sma_col]
        
        return df
    
    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based features."""
        
        # Volume moving averages
        for period in [5, 10, 20, 50]:
            df[f'volume_sma_{period}'] = df['volume'].rolling(period).mean()
        
        # Volume ratios
        df['volume_ratio_5'] = df['volume'] / df['volume'].rolling(5).mean()
        df['volume_ratio_20'] = df['volume'] / df['volume'].rolling(20).mean()
        
        # Price-volume features
        df['price_volume'] = df['close'] * df['volume']
        df['vwap'] = (df['price_volume'].rolling(20).sum() / 
                     df['volume'].rolling(20).sum())
        
        return df
    
    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features."""
        
        # Historical volatility
        for period in [5, 10, 20, 30]:
            returns = df['close'].pct_change()
            df[f'volatility_{period}d'] = returns.rolling(period).std() * np.sqrt(252)
        
        # True Range
        df['true_range'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )
        
        return df
    
    def _add_fundamental_features(self, df: pd.DataFrame, company_id: int, session: Session) -> pd.DataFrame:
        """Add fundamental analysis features."""
        
        # Get latest financial data
        latest_financial = session.query(FinancialStatement).filter(
            FinancialStatement.company_id == company_id
        ).order_by(FinancialStatement.period_end.desc()).first()
        
        if latest_financial:
            # Add fundamental ratios as constant features
            df['pe_ratio'] = latest_financial.pe_ratio
            df['pb_ratio'] = latest_financial.pb_ratio
            df['debt_to_equity'] = latest_financial.debt_to_equity
            df['roe'] = latest_financial.roe
            df['roa'] = latest_financial.roa
            df['current_ratio'] = latest_financial.current_ratio
            df['revenue_growth'] = latest_financial.revenue_growth
            df['profit_growth'] = latest_financial.profit_growth
        
        return df
    
    def _add_market_features(self, df: pd.DataFrame, session: Session) -> pd.DataFrame:
        """Add market-wide features."""
        
        # This would include market indices, sector performance, etc.
        # For now, adding placeholder features
        df['market_trend'] = df['close'].rolling(20).mean().pct_change(5)
        
        return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features."""
        
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month
        df['quarter'] = df['timestamp'].dt.quarter
        df['is_month_end'] = df['timestamp'].dt.is_month_end.astype(int)
        df['is_quarter_end'] = df['timestamp'].dt.is_quarter_end.astype(int)
        
        return df
    
    def train_models(self, company_id: int, target_days: int = 5) -> Dict[str, float]:
        """Train all models with cross-validation."""
        logger.info(f"Training models for company {company_id}, target: {target_days} days")
        
        # Prepare data
        df = self.prepare_features(company_id)
        if df.empty:
            logger.error("No data available for training")
            return {}
        
        # Prepare target variable
        df['target'] = df['close'].shift(-target_days)
        df = df.dropna()
        
        if len(df) < 100:
            logger.error("Insufficient data for training")
            return {}
        
        # Feature selection
        feature_cols = [col for col in df.columns if col not in 
                       ['timestamp', 'target', 'open', 'high', 'low', 'close']]
        
        X = df[feature_cols]
        y = df['target']
        
        # Time series split for validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        results = {}
        
        for model_name, model in self.models.items():
            try:
                logger.info(f"Training {model_name}...")
                
                # Scale features for linear models
                if model_name in ['ridge', 'lasso']:
                    scaler = self.scalers['standard']
                    X_scaled = scaler.fit_transform(X)
                    self.scalers[f'{model_name}_{company_id}'] = scaler
                else:
                    X_scaled = X
                
                # Cross-validation
                cv_scores = cross_val_score(
                    model, X_scaled, y, cv=tscv, 
                    scoring='neg_mean_squared_error', n_jobs=-1
                )
                
                # Train on full dataset
                model.fit(X_scaled, y)
                
                # Store model
                model_key = f'{model_name}_{company_id}'
                self.models[model_key] = model
                
                # Calculate performance metrics
                rmse = np.sqrt(-cv_scores.mean())
                results[model_name] = {
                    'rmse': rmse,
                    'cv_std': cv_scores.std(),
                    'r2': r2_score(y, model.predict(X_scaled))
                }
                
                # Feature importance
                if hasattr(model, 'feature_importances_'):
                    importance = dict(zip(feature_cols, model.feature_importances_))
                    self.feature_importance[model_key] = importance
                
                logger.info(f"{model_name} RMSE: {rmse:.4f}")
                
            except Exception as e:
                logger.error(f"Error training {model_name}: {e}")
        
        # Save models
        self._save_models(company_id)
        
        return results
    
    def predict_ensemble(self, company_id: int, target_days: int = 5) -> Dict[str, Any]:
        """Make ensemble predictions with confidence intervals."""
        logger.info(f"Making ensemble prediction for company {company_id}")
        
        # Prepare latest data
        df = self.prepare_features(company_id)
        if df.empty:
            return {}
        
        # Get latest features
        feature_cols = [col for col in df.columns if col not in 
                       ['timestamp', 'open', 'high', 'low', 'close']]
        
        latest_features = df[feature_cols].iloc[-1:].values
        
        predictions = []
        model_weights = []
        
        for model_name in ['xgboost', 'lightgbm', 'catboost', 'random_forest']:
            model_key = f'{model_name}_{company_id}'
            
            if model_key in self.models:
                model = self.models[model_key]
                
                # Scale features if needed
                if model_name in ['ridge', 'lasso']:
                    scaler_key = f'{model_name}_{company_id}'
                    if scaler_key in self.scalers:
                        features = self.scalers[scaler_key].transform(latest_features)
                    else:
                        continue
                else:
                    features = latest_features
                
                pred = model.predict(features)[0]
                predictions.append(pred)
                
                # Weight based on model performance
                if model_key in self.model_performance:
                    weight = 1.0 / (1.0 + self.model_performance[model_key].get('rmse', 1.0))
                else:
                    weight = 1.0
                model_weights.append(weight)
        
        if not predictions:
            return {}
        
        # Ensemble prediction
        predictions = np.array(predictions)
        weights = np.array(model_weights)
        weights = weights / weights.sum()
        
        ensemble_pred = np.average(predictions, weights=weights)
        
        # Confidence interval (simplified)
        pred_std = np.std(predictions)
        confidence_lower = ensemble_pred - 1.96 * pred_std
        confidence_upper = ensemble_pred + 1.96 * pred_std
        
        return {
            'predicted_price': ensemble_pred,
            'confidence_lower': confidence_lower,
            'confidence_upper': confidence_upper,
            'individual_predictions': dict(zip(
                [f'model_{i}' for i in range(len(predictions))], 
                predictions.tolist()
            )),
            'model_weights': dict(zip(
                [f'weight_{i}' for i in range(len(weights))], 
                weights.tolist()
            ))
        }
    
    def optimize_hyperparameters(self, company_id: int, model_name: str = 'xgboost') -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna."""
        logger.info(f"Optimizing hyperparameters for {model_name}")
        
        df = self.prepare_features(company_id)
        if df.empty:
            return {}
        
        df['target'] = df['close'].shift(-5)
        df = df.dropna()
        
        feature_cols = [col for col in df.columns if col not in 
                       ['timestamp', 'target', 'open', 'high', 'low', 'close']]
        
        X = df[feature_cols].values
        y = df['target'].values
        
        def objective(trial):
            if model_name == 'xgboost':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                }
                model = xgb.XGBRegressor(**params, random_state=42)
            
            tscv = TimeSeriesSplit(n_splits=3)
            scores = cross_val_score(model, X, y, cv=tscv, scoring='neg_mean_squared_error')
            return scores.mean()
        
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=50)
        
        return {
            'best_params': study.best_params,
            'best_score': study.best_value,
            'n_trials': len(study.trials)
        }
    
    def _save_models(self, company_id: int):
        """Save trained models to disk."""
        model_file = self.models_dir / f"models_company_{company_id}.joblib"
        
        company_models = {
            key: model for key, model in self.models.items() 
            if str(company_id) in key
        }
        
        joblib.dump(company_models, model_file)
        logger.info(f"Models saved to {model_file}")
    
    def load_models(self, company_id: int) -> bool:
        """Load trained models from disk."""
        model_file = self.models_dir / f"models_company_{company_id}.joblib"
        
        if not model_file.exists():
            return False
        
        try:
            loaded_models = joblib.load(model_file)
            self.models.update(loaded_models)
            logger.info(f"Models loaded from {model_file}")
            return True
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False

class RealTimeDataEnhancer:
    """Enhanced ML predictor with real-time API data integration."""

    def __init__(self):
        self.polygon_client = None
        self.alpha_vantage_ts = None
        self.alpha_vantage_ti = None

        # Initialize API clients
        self._init_polygon_client()
        self._init_alpha_vantage_client()

        # Base predictor
        self.base_predictor = AdvancedFinancialPredictor()

        logger.info("RealTimeDataEnhancer initialized")

    def _init_polygon_client(self):
        """Initialize Polygon.io client."""
        if POLYGON_AVAILABLE:
            api_key = os.getenv('POLYGON_API_KEY')
            if api_key:
                try:
                    self.polygon_client = RESTClient(api_key)
                    logger.info("Polygon.io client initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Polygon client: {e}")
            else:
                logger.warning("POLYGON_API_KEY not found in environment variables")
        else:
            logger.warning("Polygon library not available")

    def _init_alpha_vantage_client(self):
        """Initialize Alpha Vantage client."""
        if ALPHA_VANTAGE_AVAILABLE:
            api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
            if api_key:
                try:
                    self.alpha_vantage_ts = TimeSeries(key=api_key, output_format='pandas')
                    self.alpha_vantage_ti = TechIndicators(key=api_key, output_format='pandas')
                    logger.info("Alpha Vantage client initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Alpha Vantage client: {e}")
            else:
                logger.warning("ALPHA_VANTAGE_API_KEY not found in environment variables")
        else:
            logger.warning("Alpha Vantage library not available")

    def get_real_time_data(self, symbol: str, days: int = 30) -> Optional[pd.DataFrame]:
        """Get real-time stock data from APIs."""
        print(f"📡 Fetching real-time data for {symbol}...")

        # Try Polygon.io first
        polygon_data = self._get_polygon_data(symbol, days)
        if polygon_data is not None and not polygon_data.empty:
            print(f"   ✅ Polygon.io: {len(polygon_data)} records")
            return polygon_data

        # Fallback to Alpha Vantage
        alpha_data = self._get_alpha_vantage_data(symbol, days)
        if alpha_data is not None and not alpha_data.empty:
            print(f"   ✅ Alpha Vantage: {len(alpha_data)} records")
            return alpha_data

        print(f"   ❌ No real-time data available for {symbol}")
        return None

    def _get_polygon_data(self, symbol: str, days: int) -> Optional[pd.DataFrame]:
        """Get data from Polygon.io API."""
        if not self.polygon_client:
            return None

        try:
            # Get historical data
            from_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            to_date = datetime.now().strftime('%Y-%m-%d')

            # Polygon API call
            aggs = self.polygon_client.get_aggs(
                ticker=symbol,
                multiplier=1,
                timespan="day",
                from_=from_date,
                to=to_date
            )

            if not aggs:
                return None

            # Convert to DataFrame
            data = []
            for agg in aggs:
                data.append({
                    'date': pd.to_datetime(agg.timestamp, unit='ms'),
                    'open': agg.open,
                    'high': agg.high,
                    'low': agg.low,
                    'close': agg.close,
                    'volume': agg.volume
                })

            df = pd.DataFrame(data)
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)

            return df

        except Exception as e:
            logger.error(f"Error fetching Polygon data for {symbol}: {e}")
            return None

    def _get_alpha_vantage_data(self, symbol: str, days: int) -> Optional[pd.DataFrame]:
        """Get data from Alpha Vantage API."""
        if not self.alpha_vantage_ts:
            return None

        try:
            # Get daily data
            data, meta_data = self.alpha_vantage_ts.get_daily_adjusted(symbol=symbol, outputsize='compact')

            if data is None or data.empty:
                return None

            # Rename columns to match our format
            data.columns = ['open', 'high', 'low', 'close', 'adjusted_close', 'volume', 'dividend', 'split']

            # Filter to requested days
            data = data.head(days)
            data.sort_index(inplace=True)

            return data[['open', 'high', 'low', 'close', 'volume']]

        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage data for {symbol}: {e}")
            return None

    def get_technical_indicators(self, symbol: str, period: int = 14) -> Dict[str, Any]:
        """Get technical indicators from Alpha Vantage."""
        if not self.alpha_vantage_ti:
            return {}

        indicators = {}

        try:
            # RSI
            rsi_data, _ = self.alpha_vantage_ti.get_rsi(symbol=symbol, interval='daily', time_period=period)
            if rsi_data is not None and not rsi_data.empty:
                indicators['rsi'] = float(rsi_data.iloc[0, 0])

            # MACD
            macd_data, _ = self.alpha_vantage_ti.get_macd(symbol=symbol, interval='daily')
            if macd_data is not None and not macd_data.empty:
                indicators['macd'] = float(macd_data.iloc[0, 0])
                indicators['macd_signal'] = float(macd_data.iloc[0, 1])
                indicators['macd_hist'] = float(macd_data.iloc[0, 2])

            # SMA
            sma_data, _ = self.alpha_vantage_ti.get_sma(symbol=symbol, interval='daily', time_period=20)
            if sma_data is not None and not sma_data.empty:
                indicators['sma_20'] = float(sma_data.iloc[0, 0])

            print(f"   📊 Technical indicators for {symbol}: {len(indicators)} indicators")

        except Exception as e:
            logger.error(f"Error fetching technical indicators for {symbol}: {e}")

        return indicators

    def get_news_sentiment(self, symbol: str, limit: int = 10) -> Dict[str, Any]:
        """Get news and sentiment data from Polygon.io."""
        if not self.polygon_client:
            return {}

        try:
            # Get news articles
            news = self.polygon_client.list_ticker_news(
                ticker=symbol,
                limit=limit
            )

            if not news:
                return {}

            # Analyze sentiment (simple keyword-based)
            positive_words = ['bullish', 'growth', 'profit', 'gain', 'rise', 'up', 'strong', 'buy']
            negative_words = ['bearish', 'loss', 'decline', 'fall', 'down', 'weak', 'sell', 'drop']

            sentiment_scores = []
            for article in news:
                title = article.title.lower() if article.title else ""
                description = article.description.lower() if article.description else ""
                text = f"{title} {description}"

                positive_count = sum(1 for word in positive_words if word in text)
                negative_count = sum(1 for word in negative_words if word in text)

                if positive_count > negative_count:
                    sentiment_scores.append(1)  # Positive
                elif negative_count > positive_count:
                    sentiment_scores.append(-1)  # Negative
                else:
                    sentiment_scores.append(0)  # Neutral

            avg_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0

            return {
                'news_count': len(news),
                'avg_sentiment': avg_sentiment,
                'sentiment_distribution': {
                    'positive': sentiment_scores.count(1),
                    'negative': sentiment_scores.count(-1),
                    'neutral': sentiment_scores.count(0)
                }
            }

        except Exception as e:
            logger.error(f"Error fetching news sentiment for {symbol}: {e}")
            return {}

# Global predictor instances
financial_predictor = AdvancedFinancialPredictor()
realtime_predictor = RealTimeDataEnhancer()
