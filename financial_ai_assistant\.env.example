# Financial AI Assistant Environment Configuration

# Application Settings
DEBUG=false
HOST=localhost
PORT=8000
STREAMLIT_PORT=8501

# API Keys (Required for full functionality)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
POLYGON_API_KEY=your_polygon_api_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///./data/storage/financial_ai.db

# LLM Configuration
DEFAULT_LLM_MODEL=gpt-4
LLM_TEMPERATURE=0.1
MAX_TOKENS=2000

# Portfolio Settings
DEFAULT_PORTFOLIO_SIZE=10
REBALANCE_THRESHOLD=0.05

# Data Refresh Intervals (in minutes)
MARKET_DATA_REFRESH=5
PORTFOLIO_REFRESH=15
