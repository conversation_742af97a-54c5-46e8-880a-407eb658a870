# 🚀 Advanced Financial AI Assistant with Portfolio Companion

A powerful financial management system built with MCP (Model Context Protocol), agentic AI, and advanced LLMs for comprehensive portfolio management and financial analysis.

## 🏗️ Architecture

```
financial_ai_assistant/
├── core/                   # Core system components
│   ├── mcp_server/        # Model Context Protocol server
│   ├── agents/            # <PERSON><PERSON><PERSON><PERSON>/LangGraph agents
│   ├── portfolio/         # Portfolio management system
│   └── llm/              # LLM integration and management
├── data/                  # Data management and storage
│   ├── database.py       # Database configuration
│   └── market_data.py    # Market data providers
├── ui/                    # Streamlit user interface
├── tests/                 # Comprehensive test suites
├── config/               # Configuration files
└── main.py               # Application entry point
```

## 🎯 Features

### Core Capabilities
- **MCP Server**: FastAPI-based Model Context Protocol server for financial data operations
- **Agentic AI**: LangChain/LangGraph-powered intelligent agents for financial analysis
- **Portfolio Management**: Comprehensive portfolio tracking, analysis, and optimization
- **LLM Integration**: Support for OpenAI GPT-4, Anthropic Claude, and other powerful models
- **Real-time Data**: Integration with Polygon.io, Alpha Vantage, and Yahoo Finance APIs
- **Interactive UI**: Modern Streamlit-based web interface

### Advanced Features
- **Risk Assessment**: Sophisticated risk analysis and management tools
- **Market Analysis**: Real-time market data and trend analysis
- **Investment Recommendations**: AI-powered investment advice and strategies
- **Portfolio Optimization**: Automated rebalancing and allocation optimization
- **Conversational Interface**: Natural language financial conversations
- **Multi-provider Support**: Flexible data source and LLM provider integration

## 📋 Requirements

### System Requirements
- Python 3.9+
- 4GB+ RAM
- Internet connection for API access

### API Keys (Optional but Recommended)
- OpenAI API key for GPT-4 access
- Anthropic API key for Claude access
- Polygon.io API key for premium market data
- Alpha Vantage API key for additional market data

## 🛠️ Installation

### 1. Clone and Setup
```bash
git clone <repository-url>
cd financial_ai_assistant
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Configure Environment
```bash
cp .env.example .env
# Edit .env with your API keys and settings
```

### 5. Validate System
```bash
python validate_system.py
```

### 6. Run Tests (Optional)
```bash
python run_tests.py
```

## 🚀 Quick Start

### Start the Complete System
```bash
python main.py
```

This will start both:
- MCP Server at `http://localhost:8000`
- Streamlit UI at `http://localhost:8501`

### Access the Application
1. Open your browser to `http://localhost:8501`
2. Navigate through the different sections:
   - **Dashboard**: Overview of your financial data
   - **Portfolio**: Manage your investment portfolios
   - **AI Chat**: Converse with the financial AI assistant
   - **Market Data**: Real-time market information
   - **Settings**: Configure API keys and preferences

## 💼 Usage Examples

### Creating a Portfolio
1. Go to the Portfolio section
2. Click "Create New Portfolio"
3. Enter portfolio details and initial cash
4. Start adding holdings and tracking performance

### AI Financial Conversations
1. Navigate to the AI Chat section
2. Select conversation type (General, Portfolio Analysis, Market Analysis, etc.)
3. Ask questions like:
   - "Analyze my portfolio performance"
   - "What are the current market trends?"
   - "Should I rebalance my portfolio?"
   - "What are good investment opportunities in tech?"

### Market Data Analysis
1. Go to Market Data section
2. Enter stock symbols (AAPL, GOOGL, etc.)
3. View real-time quotes and historical charts
4. Get AI-powered market insights

## 🔧 Configuration

### Environment Variables (.env)
```env
# API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
POLYGON_API_KEY=your_polygon_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# Server Configuration
HOST=localhost
PORT=8000
STREAMLIT_PORT=8501

# Database
DATABASE_URL=sqlite:///./data/storage/financial_ai.db

# LLM Settings
DEFAULT_LLM_MODEL=gpt-4
LLM_TEMPERATURE=0.1
```

### Customization
- Modify `config/settings.py` for advanced configuration
- Extend agents in `core/agents/` for custom functionality
- Add new data providers in `data/market_data.py`
- Customize UI in `ui/main.py`

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py
```

### Run Specific Tests
```bash
pytest tests/test_portfolio.py -v
pytest tests/test_mcp_server.py -v
```

### System Validation
```bash
python validate_system.py
```

## 📊 Features in Detail

### MCP Server
- RESTful API endpoints for financial operations
- Real-time market data integration
- Portfolio management operations
- AI-powered analysis endpoints

### Agentic AI System
- Base agent framework for extensibility
- Specialized portfolio management agent
- Tool integration for market analysis
- Conversational AI capabilities

### Portfolio Management
- Multi-portfolio support
- Real-time performance tracking
- Risk analysis and metrics
- Automated rebalancing suggestions
- Transaction history and reporting

### LLM Integration
- Multi-provider support (OpenAI, Anthropic)
- Conversation memory management
- Context-aware financial advice
- Streaming responses for real-time interaction

## 🔒 Security & Privacy

- Local data storage by default
- API keys stored in environment variables
- No sensitive data transmitted without encryption
- User data isolation and privacy protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please:
1. Check the documentation
2. Run `python validate_system.py` to diagnose issues
3. Review the logs for error messages
4. Open an issue on GitHub

## 🔮 Roadmap

- [ ] Advanced ML models for prediction
- [ ] Mobile app integration
- [ ] Social trading features
- [ ] Advanced charting and technical analysis
- [ ] Integration with more brokers and exchanges
- [ ] Multi-language support

---

**Built with ❤️ for the financial AI community**
