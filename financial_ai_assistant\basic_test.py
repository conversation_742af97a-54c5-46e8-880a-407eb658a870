"""
Basic test to validate core system components
"""
import sys
import os
from pathlib import Path
import asyncio

# Add project root to path
sys.path.append(str(Path(__file__).parent))

print("🚀 Financial AI Assistant - Basic System Test")
print("=" * 60)

async def test_basic_functionality():
    """Test basic system functionality."""
    
    # Test 1: Configuration
    print("\n⚙️  Testing Configuration...")
    try:
        from config.settings import settings
        print("   ✅ Settings loaded successfully")
        print(f"   📁 Base directory: {settings.BASE_DIR}")
        print(f"   📁 Models directory: {settings.MODELS_DIR}")
        
        # Ensure directories exist
        settings.MODELS_DIR.mkdir(parents=True, exist_ok=True)
        settings.DATA_DIR.mkdir(parents=True, exist_ok=True)
        settings.STORAGE_DIR.mkdir(parents=True, exist_ok=True)
        
        print("   ✅ All directories created/verified")
        
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        return False
    
    # Test 2: Basic ML Components
    print("\n🤖 Testing ML Components...")
    try:
        # Test basic imports
        import pandas as pd
        import numpy as np
        print("   ✅ Core data science libraries available")
        
        # Test basic ML functionality
        from core.ml.predictor import AdvancedMLPredictor
        predictor = AdvancedMLPredictor()
        print("   ✅ ML Predictor class instantiated")
        
        # Test basic data structures
        sample_data = pd.DataFrame({
            'price': np.random.randn(100) * 10 + 100,
            'volume': np.random.randint(1000, 10000, 100),
            'returns': np.random.randn(100) * 0.02
        })
        
        print(f"   ✅ Sample data created: {len(sample_data)} rows")
        
    except Exception as e:
        print(f"   ❌ ML components error: {e}")
        return False
    
    # Test 3: LLM Manager
    print("\n🧠 Testing LLM Manager...")
    try:
        from core.llm.manager import FinancialLLMManager
        llm_manager = FinancialLLMManager()
        print("   ✅ LLM Manager instantiated")
        
        # Test provider configuration (without actual API calls)
        provider_count = len(llm_manager.providers)
        print(f"   📊 LLM providers configured: {provider_count}")
        
        if provider_count == 0:
            print("   ⚠️  No API keys configured - LLM features will be limited")
        
    except Exception as e:
        print(f"   ❌ LLM manager error: {e}")
        return False
    
    # Test 4: Data Pipeline
    print("\n📊 Testing Data Pipeline...")
    try:
        from data.market_data import MarketDataManager
        data_manager = MarketDataManager()
        print("   ✅ Market Data Manager instantiated")
        
        # Test basic data structures
        from data.models import Portfolio, Holding
        print("   ✅ Data models imported successfully")
        
    except Exception as e:
        print(f"   ❌ Data pipeline error: {e}")
        return False
    
    # Test 5: MCP Server
    print("\n🔗 Testing MCP Server...")
    try:
        from core.mcp_server.server import app
        print("   ✅ MCP Server app created")
        
        # Test basic FastAPI functionality
        from fastapi.testclient import TestClient
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            print("   ✅ Health endpoint working")
        else:
            print(f"   ⚠️  Health endpoint returned: {response.status_code}")
        
    except Exception as e:
        print(f"   ❌ MCP server error: {e}")
        return False
    
    return True

async def test_ml_prediction_pipeline():
    """Test ML prediction pipeline with mock data."""
    print("\n🎯 Testing ML Prediction Pipeline...")
    
    try:
        from core.ml.predictor import ml_predictor
        
        # Test feature engineering with mock data
        print("   Testing feature engineering...")
        
        # Create mock price data
        import pandas as pd
        import numpy as np
        
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        mock_data = pd.DataFrame({
            'Date': dates,
            'Open': np.random.randn(100) * 2 + 100,
            'High': np.random.randn(100) * 2 + 102,
            'Low': np.random.randn(100) * 2 + 98,
            'Close': np.random.randn(100) * 2 + 100,
            'Volume': np.random.randint(1000000, 10000000, 100)
        })
        
        # Test basic feature creation
        features = ml_predictor._create_technical_features(mock_data)
        
        if len(features.columns) > 10:
            print(f"   ✅ Feature engineering: {len(features.columns)} features created")
        else:
            print(f"   ⚠️  Limited features created: {len(features.columns)}")
        
        # Test model training preparation
        X, y = ml_predictor._prepare_training_data(features, target_days=5)
        
        if len(X) > 0 and len(y) > 0:
            print(f"   ✅ Training data prepared: {len(X)} samples")
        else:
            print(f"   ❌ Training data preparation failed")
            return False
        
        print("   ✅ ML prediction pipeline functional")
        return True
        
    except Exception as e:
        print(f"   ❌ ML prediction pipeline error: {e}")
        return False

async def main():
    """Run all basic tests."""
    
    # Run basic functionality tests
    basic_success = await test_basic_functionality()
    
    # Run ML pipeline tests
    ml_success = await test_ml_prediction_pipeline()
    
    # Final assessment
    print("\n" + "=" * 60)
    print("📋 BASIC TEST SUMMARY")
    print("=" * 60)
    
    if basic_success and ml_success:
        print("✅ SYSTEM READY - All basic components functional")
        print("🚀 Ready for comprehensive ML testing")
        print("\nNext steps:")
        print("   1. Install remaining packages: pip install -r requirements.txt")
        print("   2. Configure API keys in .env file")
        print("   3. Run complete test: python run_complete_test.py")
    elif basic_success:
        print("⚠️  PARTIAL SUCCESS - Basic components work, ML needs attention")
        print("🔧 Fix ML pipeline issues before proceeding")
    else:
        print("❌ SYSTEM NEEDS WORK - Basic components have issues")
        print("🔧 Fix configuration and import issues first")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
