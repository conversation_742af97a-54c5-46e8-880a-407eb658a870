"""
Configuration settings for Financial AI Assistant
"""
import os
from pathlib import Path
from typing import Optional
from pydantic import BaseSettings, Field

class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    APP_NAME: str = "Financial AI Assistant"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server Configuration
    HOST: str = Field(default="localhost", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    STREAMLIT_PORT: int = Field(default=8501, env="STREAMLIT_PORT")
    
    # API Keys
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    POLYGON_API_KEY: Optional[str] = Field(default=None, env="POLYGON_API_KEY")
    ALPHA_VANTAGE_API_KEY: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    
    # Database
    DATABASE_URL: str = Field(default="sqlite:///./data/storage/financial_ai.db", env="DATABASE_URL")
    
    # MCP Server
    MCP_SERVER_URL: str = Field(default="http://localhost:8000", env="MCP_SERVER_URL")
    
    # LLM Configuration
    DEFAULT_LLM_MODEL: str = Field(default="gpt-4", env="DEFAULT_LLM_MODEL")
    LLM_TEMPERATURE: float = Field(default=0.1, env="LLM_TEMPERATURE")
    MAX_TOKENS: int = Field(default=2000, env="MAX_TOKENS")
    
    # Portfolio Settings
    DEFAULT_PORTFOLIO_SIZE: int = Field(default=10, env="DEFAULT_PORTFOLIO_SIZE")
    REBALANCE_THRESHOLD: float = Field(default=0.05, env="REBALANCE_THRESHOLD")
    
    # Data Refresh Intervals (in minutes)
    MARKET_DATA_REFRESH: int = Field(default=5, env="MARKET_DATA_REFRESH")
    PORTFOLIO_REFRESH: int = Field(default=15, env="PORTFOLIO_REFRESH")
    
    # Paths
    BASE_DIR: Path = Path(__file__).parent.parent
    DATA_DIR: Path = BASE_DIR / "data"
    MODELS_DIR: Path = DATA_DIR / "models"
    DATASETS_DIR: Path = DATA_DIR / "datasets"
    STORAGE_DIR: Path = DATA_DIR / "storage"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
settings = Settings()

# Ensure directories exist
for directory in [settings.DATA_DIR, settings.MODELS_DIR, settings.DATASETS_DIR, settings.STORAGE_DIR]:
    directory.mkdir(parents=True, exist_ok=True)
