"""
Base Agent class for Financial AI Assistant
Provides common functionality for all financial agents
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime

from langchain.agents import AgentExecutor
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from langchain.chat_models import ChatOpenAI
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import BaseMessage
from langchain.tools import BaseTool

from config.settings import settings

logger = logging.getLogger(__name__)

class BaseFinancialAgent(ABC):
    """Base class for all financial agents."""
    
    def __init__(self, name: str, description: str, tools: List[BaseTool] = None):
        self.name = name
        self.description = description
        self.tools = tools or []
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model=settings.DEFAULT_LLM_MODEL,
            temperature=settings.LLM_TEMPERATURE,
            max_tokens=settings.MAX_TOKENS,
            openai_api_key=settings.OPENAI_API_KEY
        )
        
        # Setup agent
        self.agent_executor = self._create_agent()
        
        logger.info(f"Initialized {self.name} agent")
    
    def _create_agent(self) -> AgentExecutor:
        """Create the agent executor."""
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            ("user", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
        
        # Bind tools to LLM
        llm_with_tools = self.llm.bind_functions(
            [tool.to_openai_function() for tool in self.tools]
        )
        
        # Create agent
        agent = (
            {
                "input": lambda x: x["input"],
                "agent_scratchpad": lambda x: format_to_openai_function_messages(
                    x["intermediate_steps"]
                ),
            }
            | prompt
            | llm_with_tools
            | OpenAIFunctionsAgentOutputParser()
        )
        
        # Create agent executor
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True
        )
    
    @abstractmethod
    def _get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        pass
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent with input data."""
        try:
            result = await self.agent_executor.ainvoke(input_data)
            return {
                "success": True,
                "result": result,
                "agent": self.name,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error executing {self.name} agent: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": self.name,
                "timestamp": datetime.now().isoformat()
            }
    
    def add_tool(self, tool: BaseTool):
        """Add a tool to the agent."""
        self.tools.append(tool)
        # Recreate agent with new tools
        self.agent_executor = self._create_agent()
        logger.info(f"Added tool {tool.name} to {self.name} agent")
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get agent capabilities."""
        return {
            "name": self.name,
            "description": self.description,
            "tools": [tool.name for tool in self.tools],
            "model": settings.DEFAULT_LLM_MODEL
        }
