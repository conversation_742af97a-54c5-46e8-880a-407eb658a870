"""
Advanced ML Financial Predictor with PKL model integration
Integrates with LLM for intelligent financial predictions
"""
import logging
import joblib
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import asyncio

# ML Libraries
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, accuracy_score
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
import xgboost as xgb
import lightgbm as lgb
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

# Technical Analysis
try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False

from config.settings import settings
from data.market_data import market_data_manager

logger = logging.getLogger(__name__)

class AdvancedMLPredictor:
    """Advanced ML predictor with 99% accuracy target and PKL integration."""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.model_performance = {}
        self.models_dir = settings.MODELS_DIR
        self.models_dir.mkdir(exist_ok=True)
        
        # Performance tracking
        self.accuracy_target = 0.99
        self.current_accuracy = {}
        
        # Initialize models
        self._initialize_models()
        logger.info("Advanced ML Predictor initialized")
    
    def _initialize_models(self):
        """Initialize high-performance ML models."""
        self.models = {
            'xgboost_optimized': xgb.XGBRegressor(
                n_estimators=2000,
                max_depth=8,
                learning_rate=0.005,
                subsample=0.85,
                colsample_bytree=0.85,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                early_stopping_rounds=100,
                n_jobs=-1
            ),
            'lightgbm_optimized': lgb.LGBMRegressor(
                n_estimators=2000,
                max_depth=8,
                learning_rate=0.005,
                subsample=0.85,
                colsample_bytree=0.85,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                verbose=-1,
                n_jobs=-1
            ),
            'random_forest_optimized': RandomForestRegressor(
                n_estimators=1000,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            )
        }
        
        if CATBOOST_AVAILABLE:
            self.models['catboost_optimized'] = cb.CatBoostRegressor(
                iterations=2000,
                depth=8,
                learning_rate=0.005,
                l2_leaf_reg=3,
                random_seed=42,
                verbose=False,
                thread_count=-1
            )
        
        self.scalers = {
            'standard': StandardScaler(),
            'robust': RobustScaler()
        }
    
    async def prepare_advanced_features(self, symbol: str, lookback_days: int = 500) -> pd.DataFrame:
        """Prepare comprehensive feature set for maximum accuracy."""
        logger.info(f"Preparing advanced features for {symbol}")
        
        try:
            # Get historical data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)
            
            df = await market_data_manager.get_historical_data(
                symbol, start_date, end_date, interval="1d"
            )
            
            if df.empty or len(df) < 100:
                logger.warning(f"Insufficient data for {symbol}")
                return pd.DataFrame()
            
            # Ensure proper column names
            df.columns = [col.lower() for col in df.columns]
            
            # Technical indicators
            df = self._add_comprehensive_technical_indicators(df)
            
            # Advanced price features
            df = self._add_advanced_price_features(df)
            
            # Volume analysis
            df = self._add_volume_analysis(df)
            
            # Volatility features
            df = self._add_volatility_features(df)
            
            # Market microstructure
            df = self._add_microstructure_features(df)
            
            # Time-based features
            df = self._add_time_features(df)
            
            # Lag features
            df = self._add_lag_features(df)
            
            # Statistical features
            df = self._add_statistical_features(df)
            
            # Remove NaN values
            df = df.dropna()
            
            logger.info(f"Prepared {len(df)} samples with {len(df.columns)} features for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"Error preparing features for {symbol}: {e}")
            return pd.DataFrame()
    
    def _add_comprehensive_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add comprehensive technical indicators for maximum accuracy."""
        
        if not TA_AVAILABLE:
            logger.warning("TA library not available, using basic indicators")
            # Basic indicators
            for period in [5, 10, 20, 50, 100, 200]:
                df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            return df
        
        try:
            # Moving averages (multiple periods)
            for period in [5, 10, 20, 50, 100, 200]:
                df[f'sma_{period}'] = ta.trend.sma_indicator(df['close'], window=period)
                df[f'ema_{period}'] = ta.trend.ema_indicator(df['close'], window=period)
                df[f'wma_{period}'] = ta.trend.wma_indicator(df['close'], window=period)
            
            # RSI (multiple periods)
            for period in [14, 21, 30]:
                df[f'rsi_{period}'] = ta.momentum.rsi(df['close'], window=period)
            
            # MACD variations
            df['macd'] = ta.trend.macd(df['close'])
            df['macd_signal'] = ta.trend.macd_signal(df['close'])
            df['macd_diff'] = ta.trend.macd_diff(df['close'])
            
            # Bollinger Bands
            df['bb_upper'] = ta.volatility.bollinger_hband(df['close'])
            df['bb_lower'] = ta.volatility.bollinger_lband(df['close'])
            df['bb_middle'] = ta.volatility.bollinger_mavg(df['close'])
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Stochastic
            df['stoch_k'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
            
            # Williams %R
            df['williams_r'] = ta.momentum.williams_r(df['high'], df['low'], df['close'])
            
            # ATR
            df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])
            
            # CCI
            df['cci'] = ta.trend.cci(df['high'], df['low'], df['close'])
            
            # ADX
            df['adx'] = ta.trend.adx(df['high'], df['low'], df['close'])
            
            # Aroon
            df['aroon_up'] = ta.trend.aroon_up(df['high'], df['low'])
            df['aroon_down'] = ta.trend.aroon_down(df['high'], df['low'])
            
            # Money Flow Index
            df['mfi'] = ta.volume.money_flow_index(df['high'], df['low'], df['close'], df['volume'])
            
            # On Balance Volume
            df['obv'] = ta.volume.on_balance_volume(df['close'], df['volume'])
            
        except Exception as e:
            logger.warning(f"Error with technical indicators: {e}")
        
        return df
    
    def _add_advanced_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add advanced price-based features."""
        
        # Returns (multiple periods)
        for period in [1, 2, 3, 5, 10, 20, 30]:
            df[f'return_{period}d'] = df['close'].pct_change(period)
            df[f'log_return_{period}d'] = np.log(df['close'] / df['close'].shift(period))
        
        # Price ratios
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        df['high_close_ratio'] = df['high'] / df['close']
        df['low_close_ratio'] = df['low'] / df['close']
        
        # Price position
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # Gap analysis
        df['gap'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)
        df['gap_filled'] = ((df['low'] <= df['close'].shift(1)) & (df['gap'] > 0)).astype(int)
        
        # Distance from moving averages
        for period in [20, 50, 200]:
            sma_col = f'sma_{period}'
            if sma_col in df.columns:
                df[f'price_vs_{sma_col}'] = (df['close'] - df[sma_col]) / df[sma_col]
        
        return df
    
    def _add_volume_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add comprehensive volume analysis."""
        
        # Volume moving averages
        for period in [5, 10, 20, 50]:
            df[f'volume_sma_{period}'] = df['volume'].rolling(period).mean()
        
        # Volume ratios
        df['volume_ratio_5'] = df['volume'] / df['volume'].rolling(5).mean()
        df['volume_ratio_20'] = df['volume'] / df['volume'].rolling(20).mean()
        
        # Price-volume features
        df['price_volume'] = df['close'] * df['volume']
        df['vwap_20'] = (df['price_volume'].rolling(20).sum() / df['volume'].rolling(20).sum())
        
        # Volume trend
        df['volume_trend'] = df['volume'].rolling(10).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])
        
        return df
    
    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features."""
        
        # Historical volatility (multiple periods)
        for period in [5, 10, 20, 30, 60]:
            returns = df['close'].pct_change()
            df[f'volatility_{period}d'] = returns.rolling(period).std() * np.sqrt(252)
        
        # True Range
        df['true_range'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )
        
        # Volatility ratios
        df['vol_ratio_short_long'] = df['volatility_5d'] / df['volatility_30d']
        
        return df
    
    def _add_microstructure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market microstructure features."""
        
        # Bid-ask spread proxy
        df['spread_proxy'] = (df['high'] - df['low']) / df['close']
        
        # Intraday momentum
        df['intraday_momentum'] = (df['close'] - df['open']) / df['open']
        
        # Price efficiency
        df['price_efficiency'] = abs(df['close'] - df['open']) / (df['high'] - df['low'])
        
        return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features."""
        
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        else:
            df['timestamp'] = df.index
        
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month
        df['quarter'] = df['timestamp'].dt.quarter
        df['is_month_end'] = df['timestamp'].dt.is_month_end.astype(int)
        df['is_quarter_end'] = df['timestamp'].dt.is_quarter_end.astype(int)
        df['is_year_end'] = df['timestamp'].dt.is_year_end.astype(int)
        
        return df
    
    def _add_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add lag features for time series patterns."""
        
        # Price lags
        for lag in [1, 2, 3, 5, 10]:
            df[f'close_lag_{lag}'] = df['close'].shift(lag)
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
        
        # Return lags
        for lag in [1, 2, 3, 5]:
            df[f'return_lag_{lag}'] = df['return_1d'].shift(lag)
        
        return df
    
    def _add_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add statistical features."""
        
        # Rolling statistics
        for window in [10, 20, 30]:
            df[f'close_mean_{window}'] = df['close'].rolling(window).mean()
            df[f'close_std_{window}'] = df['close'].rolling(window).std()
            df[f'close_skew_{window}'] = df['close'].rolling(window).skew()
            df[f'close_kurt_{window}'] = df['close'].rolling(window).kurt()
        
        return df

    async def train_high_accuracy_models(self, symbol: str, target_days: int = 5) -> Dict[str, Any]:
        """Train models with 99% accuracy target."""
        logger.info(f"Training high-accuracy models for {symbol}, target: {target_days} days")

        # Prepare comprehensive features
        df = await self.prepare_advanced_features(symbol)
        if df.empty:
            logger.error(f"No data available for training {symbol}")
            return {}

        # Prepare target variable (price direction and magnitude)
        df['target_price'] = df['close'].shift(-target_days)
        df['target_return'] = (df['target_price'] - df['close']) / df['close']
        df['target_direction'] = (df['target_return'] > 0).astype(int)

        # Remove rows with NaN targets
        df = df.dropna()

        if len(df) < 200:
            logger.error(f"Insufficient data for training {symbol}")
            return {}

        # Feature selection (remove non-predictive columns)
        exclude_cols = ['timestamp', 'target_price', 'target_return', 'target_direction',
                       'open', 'high', 'low', 'close', 'volume']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        X = df[feature_cols]
        y_price = df['target_price']
        y_direction = df['target_direction']

        # Time series split for validation
        tscv = TimeSeriesSplit(n_splits=10)

        results = {}

        for model_name, model in self.models.items():
            try:
                logger.info(f"Training {model_name} for {symbol}...")

                # Cross-validation for price prediction
                cv_scores = cross_val_score(
                    model, X, y_price, cv=tscv,
                    scoring='neg_mean_squared_error', n_jobs=-1
                )

                # Train on full dataset
                model.fit(X, y_price)

                # Predictions for accuracy calculation
                y_pred = model.predict(X)

                # Calculate comprehensive metrics
                rmse = np.sqrt(mean_squared_error(y_price, y_pred))
                mae = mean_absolute_error(y_price, y_pred)
                r2 = r2_score(y_price, y_pred)

                # Direction accuracy
                pred_direction = (y_pred > df['close']).astype(int)
                direction_accuracy = accuracy_score(y_direction, pred_direction)

                # Price accuracy (within 5% tolerance)
                price_accuracy = np.mean(np.abs((y_pred - y_price) / y_price) < 0.05)

                # Store model with symbol key
                model_key = f'{model_name}_{symbol}'
                self.models[model_key] = model

                results[model_name] = {
                    'rmse': rmse,
                    'mae': mae,
                    'r2': r2,
                    'direction_accuracy': direction_accuracy,
                    'price_accuracy': price_accuracy,
                    'cv_mean': -cv_scores.mean(),
                    'cv_std': cv_scores.std()
                }

                # Track current accuracy
                self.current_accuracy[model_key] = price_accuracy

                # Feature importance
                if hasattr(model, 'feature_importances_'):
                    importance = dict(zip(feature_cols, model.feature_importances_))
                    self.feature_importance[model_key] = importance

                logger.info(f"{model_name} - Price Accuracy: {price_accuracy:.4f}, Direction Accuracy: {direction_accuracy:.4f}")

            except Exception as e:
                logger.error(f"Error training {model_name} for {symbol}: {e}")

        # Save models to PKL files
        await self._save_models_pkl(symbol, results)

        return results

    async def _save_models_pkl(self, symbol: str, results: Dict[str, Any]):
        """Save trained models to PKL files with metadata."""
        try:
            # Create symbol-specific directory
            symbol_dir = self.models_dir / symbol
            symbol_dir.mkdir(exist_ok=True)

            # Save individual models
            for model_name in self.models:
                if symbol in model_name:
                    model_file = symbol_dir / f"{model_name}.pkl"
                    joblib.dump(self.models[model_name], model_file)

            # Save scalers
            scaler_file = symbol_dir / f"scalers_{symbol}.pkl"
            symbol_scalers = {k: v for k, v in self.scalers.items() if symbol in k}
            if symbol_scalers:
                joblib.dump(symbol_scalers, scaler_file)

            # Save feature importance
            importance_file = symbol_dir / f"feature_importance_{symbol}.pkl"
            symbol_importance = {k: v for k, v in self.feature_importance.items() if symbol in k}
            if symbol_importance:
                joblib.dump(symbol_importance, importance_file)

            # Save performance metrics
            performance_file = symbol_dir / f"performance_{symbol}.pkl"
            joblib.dump(results, performance_file)

            # Save metadata
            metadata = {
                'symbol': symbol,
                'training_date': datetime.now().isoformat(),
                'models_trained': list(results.keys()),
                'best_accuracy': max([r.get('price_accuracy', 0) for r in results.values()]),
                'target_accuracy': self.accuracy_target
            }

            metadata_file = symbol_dir / f"metadata_{symbol}.pkl"
            joblib.dump(metadata, metadata_file)

            logger.info(f"Models saved to PKL files in {symbol_dir}")

        except Exception as e:
            logger.error(f"Error saving models to PKL: {e}")

    async def load_models_pkl(self, symbol: str) -> bool:
        """Load trained models from PKL files."""
        try:
            symbol_dir = self.models_dir / symbol
            if not symbol_dir.exists():
                logger.warning(f"No saved models found for {symbol}")
                return False

            # Load models
            for model_file in symbol_dir.glob("*_optimized_*.pkl"):
                if model_file.stem.endswith(symbol):
                    continue  # Skip non-model files

                model_name = model_file.stem
                self.models[model_name] = joblib.load(model_file)

            # Load scalers
            scaler_file = symbol_dir / f"scalers_{symbol}.pkl"
            if scaler_file.exists():
                loaded_scalers = joblib.load(scaler_file)
                self.scalers.update(loaded_scalers)

            # Load feature importance
            importance_file = symbol_dir / f"feature_importance_{symbol}.pkl"
            if importance_file.exists():
                loaded_importance = joblib.load(importance_file)
                self.feature_importance.update(loaded_importance)

            # Load performance metrics
            performance_file = symbol_dir / f"performance_{symbol}.pkl"
            if performance_file.exists():
                self.model_performance[symbol] = joblib.load(performance_file)

            logger.info(f"Models loaded from PKL files for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Error loading models from PKL: {e}")
            return False

    async def predict_with_ensemble(self, symbol: str, target_days: int = 5) -> Dict[str, Any]:
        """Make ensemble predictions with confidence intervals."""
        logger.info(f"Making ensemble prediction for {symbol}")

        # Load models if not in memory
        if not any(symbol in key for key in self.models.keys()):
            await self.load_models_pkl(symbol)

        # Prepare latest features
        df = await self.prepare_advanced_features(symbol, lookback_days=100)
        if df.empty:
            return {}

        # Get feature columns (same as training)
        exclude_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        latest_features = df[feature_cols].iloc[-1:].values
        current_price = df['close'].iloc[-1]

        predictions = []
        model_weights = []
        model_names = []

        # Get predictions from all trained models
        for model_key, model in self.models.items():
            if symbol in model_key and 'optimized' in model_key:
                try:
                    pred = model.predict(latest_features)[0]
                    predictions.append(pred)

                    # Weight based on accuracy
                    accuracy = self.current_accuracy.get(model_key, 0.5)
                    weight = accuracy ** 2  # Square to emphasize high accuracy models
                    model_weights.append(weight)
                    model_names.append(model_key)

                except Exception as e:
                    logger.warning(f"Error predicting with {model_key}: {e}")

        if not predictions:
            return {}

        # Ensemble prediction
        predictions = np.array(predictions)
        weights = np.array(model_weights)
        weights = weights / weights.sum()

        ensemble_pred = np.average(predictions, weights=weights)

        # Calculate confidence metrics
        pred_std = np.std(predictions)
        confidence_lower = ensemble_pred - 1.96 * pred_std
        confidence_upper = ensemble_pred + 1.96 * pred_std

        # Calculate expected return and direction
        expected_return = (ensemble_pred - current_price) / current_price
        predicted_direction = "UP" if expected_return > 0 else "DOWN"
        confidence_score = min(weights.max(), 0.99)  # Cap at 99%

        return {
            'symbol': symbol,
            'current_price': current_price,
            'predicted_price': ensemble_pred,
            'expected_return': expected_return,
            'predicted_direction': predicted_direction,
            'confidence_score': confidence_score,
            'confidence_lower': confidence_lower,
            'confidence_upper': confidence_upper,
            'individual_predictions': dict(zip(model_names, predictions.tolist())),
            'model_weights': dict(zip(model_names, weights.tolist())),
            'prediction_date': datetime.now().isoformat(),
            'target_days': target_days
        }

# Global ML predictor instance
ml_predictor = AdvancedMLPredictor()
