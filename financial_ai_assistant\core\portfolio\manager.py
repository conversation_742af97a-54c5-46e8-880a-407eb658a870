"""
Portfolio Manager - Core portfolio management functionality
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import numpy as np
import pandas as pd
from sqlalchemy.orm import Session

from .models import (
    Portfolio, Holding, Transaction, PortfolioCreate, PortfolioUpdate,
    HoldingCreate, TransactionCreate, PortfolioSummary, PortfolioAnalytics,
    HoldingDetail, TransactionType
)
from data.database import get_db_session
from config.settings import settings

logger = logging.getLogger(__name__)

class PortfolioManager:
    """Core portfolio management system."""
    
    def __init__(self):
        self.db_session = get_db_session()
        logger.info("Portfolio Manager initialized")
    
    async def create_portfolio(self, portfolio_data: PortfolioCreate) -> str:
        """Create a new portfolio."""
        try:
            portfolio_id = f"portfolio_{portfolio_data.user_id}_{int(datetime.now().timestamp())}"
            
            portfolio = Portfolio(
                id=portfolio_id,
                user_id=portfolio_data.user_id,
                name=portfolio_data.name,
                description=portfolio_data.description,
                portfolio_type=portfolio_data.portfolio_type.value,
                cash_balance=portfolio_data.initial_cash,
                total_value=portfolio_data.initial_cash
            )
            
            self.db_session.add(portfolio)
            self.db_session.commit()
            
            logger.info(f"Created portfolio {portfolio_id} for user {portfolio_data.user_id}")
            return portfolio_id
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Error creating portfolio: {e}")
            raise
    
    async def get_portfolio(self, portfolio_id: str) -> Optional[Portfolio]:
        """Get portfolio by ID."""
        try:
            portfolio = self.db_session.query(Portfolio).filter(
                Portfolio.id == portfolio_id,
                Portfolio.is_active == True
            ).first()
            return portfolio
        except Exception as e:
            logger.error(f"Error retrieving portfolio {portfolio_id}: {e}")
            return None
    
    async def update_portfolio(self, portfolio_id: str, update_data: PortfolioUpdate) -> bool:
        """Update portfolio information."""
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return False
            
            if update_data.name:
                portfolio.name = update_data.name
            if update_data.description:
                portfolio.description = update_data.description
            if update_data.portfolio_type:
                portfolio.portfolio_type = update_data.portfolio_type.value
            
            portfolio.updated_at = datetime.utcnow()
            self.db_session.commit()
            
            logger.info(f"Updated portfolio {portfolio_id}")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Error updating portfolio {portfolio_id}: {e}")
            return False
    
    async def add_holding(self, portfolio_id: str, holding_data: HoldingCreate) -> bool:
        """Add a new holding to portfolio."""
        try:
            # Check if holding already exists
            existing_holding = self.db_session.query(Holding).filter(
                Holding.portfolio_id == portfolio_id,
                Holding.symbol == holding_data.symbol
            ).first()
            
            if existing_holding:
                # Update existing holding (average cost calculation)
                total_cost = (existing_holding.quantity * existing_holding.average_cost + 
                             holding_data.quantity * holding_data.average_cost)
                total_quantity = existing_holding.quantity + holding_data.quantity
                existing_holding.average_cost = total_cost / total_quantity
                existing_holding.quantity = total_quantity
                existing_holding.updated_at = datetime.utcnow()
            else:
                # Create new holding
                holding = Holding(
                    portfolio_id=portfolio_id,
                    symbol=holding_data.symbol,
                    quantity=holding_data.quantity,
                    average_cost=holding_data.average_cost,
                    asset_class=holding_data.asset_class.value,
                    sector=holding_data.sector
                )
                self.db_session.add(holding)
            
            # Record transaction
            transaction = Transaction(
                portfolio_id=portfolio_id,
                symbol=holding_data.symbol,
                transaction_type=TransactionType.BUY.value,
                quantity=holding_data.quantity,
                price=holding_data.average_cost,
                total_amount=holding_data.quantity * holding_data.average_cost
            )
            self.db_session.add(transaction)
            
            # Update portfolio cash balance
            portfolio = await self.get_portfolio(portfolio_id)
            if portfolio:
                portfolio.cash_balance -= holding_data.quantity * holding_data.average_cost
                portfolio.updated_at = datetime.utcnow()
            
            self.db_session.commit()
            logger.info(f"Added holding {holding_data.symbol} to portfolio {portfolio_id}")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Error adding holding to portfolio {portfolio_id}: {e}")
            return False
    
    async def get_portfolio_summary(self, portfolio_id: str) -> Optional[PortfolioSummary]:
        """Get portfolio summary with key metrics."""
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return None
            
            holdings = self.db_session.query(Holding).filter(
                Holding.portfolio_id == portfolio_id
            ).all()
            
            # Calculate metrics
            total_market_value = sum(h.market_value for h in holdings)
            total_cost = sum(h.quantity * h.average_cost for h in holdings)
            total_return = total_market_value - total_cost
            total_return_pct = (total_return / total_cost * 100) if total_cost > 0 else 0
            
            # TODO: Calculate day change (requires price history)
            day_change = 0.0
            day_change_pct = 0.0
            
            return PortfolioSummary(
                id=portfolio.id,
                name=portfolio.name,
                portfolio_type=portfolio.portfolio_type,
                total_value=total_market_value + portfolio.cash_balance,
                cash_balance=portfolio.cash_balance,
                total_return=total_return,
                total_return_pct=total_return_pct,
                day_change=day_change,
                day_change_pct=day_change_pct,
                holdings_count=len(holdings),
                last_updated=portfolio.updated_at
            )
            
        except Exception as e:
            logger.error(f"Error getting portfolio summary {portfolio_id}: {e}")
            return None
    
    async def get_portfolio_analytics(self, portfolio_id: str) -> Optional[PortfolioAnalytics]:
        """Get detailed portfolio analytics."""
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return None
            
            holdings = self.db_session.query(Holding).filter(
                Holding.portfolio_id == portfolio_id
            ).all()
            
            if not holdings:
                return None
            
            # Calculate basic metrics
            total_market_value = sum(h.market_value for h in holdings)
            total_cost = sum(h.quantity * h.average_cost for h in holdings)
            total_return = total_market_value - total_cost
            total_return_pct = (total_return / total_cost * 100) if total_cost > 0 else 0
            
            # Asset allocation
            asset_allocation = {}
            for holding in holdings:
                asset_class = holding.asset_class
                if asset_class not in asset_allocation:
                    asset_allocation[asset_class] = 0
                asset_allocation[asset_class] += holding.market_value
            
            # Convert to percentages
            for asset_class in asset_allocation:
                asset_allocation[asset_class] = (asset_allocation[asset_class] / total_market_value * 100) if total_market_value > 0 else 0
            
            # Sector allocation
            sector_allocation = {}
            for holding in holdings:
                sector = holding.sector or "Unknown"
                if sector not in sector_allocation:
                    sector_allocation[sector] = 0
                sector_allocation[sector] += holding.market_value
            
            # Convert to percentages
            for sector in sector_allocation:
                sector_allocation[sector] = (sector_allocation[sector] / total_market_value * 100) if total_market_value > 0 else 0
            
            # Top holdings
            top_holdings = []
            for holding in sorted(holdings, key=lambda h: h.market_value, reverse=True)[:10]:
                unrealized_gain_loss = holding.market_value - (holding.quantity * holding.average_cost)
                unrealized_gain_loss_pct = (unrealized_gain_loss / (holding.quantity * holding.average_cost) * 100) if holding.average_cost > 0 else 0
                weight = (holding.market_value / total_market_value * 100) if total_market_value > 0 else 0
                
                top_holdings.append(HoldingDetail(
                    symbol=holding.symbol,
                    quantity=holding.quantity,
                    average_cost=holding.average_cost,
                    current_price=holding.current_price,
                    market_value=holding.market_value,
                    unrealized_gain_loss=unrealized_gain_loss,
                    unrealized_gain_loss_pct=unrealized_gain_loss_pct,
                    weight=weight,
                    asset_class=holding.asset_class,
                    sector=holding.sector
                ))
            
            # TODO: Calculate advanced metrics (Sharpe, Beta, etc.) - requires price history
            
            return PortfolioAnalytics(
                total_value=total_market_value + portfolio.cash_balance,
                total_return=total_return,
                total_return_pct=total_return_pct,
                sharpe_ratio=None,  # TODO: Calculate
                volatility=None,    # TODO: Calculate
                beta=None,          # TODO: Calculate
                alpha=None,         # TODO: Calculate
                max_drawdown=None,  # TODO: Calculate
                asset_allocation=asset_allocation,
                sector_allocation=sector_allocation,
                top_holdings=top_holdings
            )
            
        except Exception as e:
            logger.error(f"Error getting portfolio analytics {portfolio_id}: {e}")
            return None
    
    async def rebalance_portfolio(self, portfolio_id: str, target_allocation: Dict[str, float]) -> bool:
        """Rebalance portfolio to target allocation."""
        try:
            # TODO: Implement portfolio rebalancing logic
            logger.info(f"Rebalancing portfolio {portfolio_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error rebalancing portfolio {portfolio_id}: {e}")
            return False
