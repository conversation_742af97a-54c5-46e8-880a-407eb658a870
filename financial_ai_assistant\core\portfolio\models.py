"""
Portfolio data models and schemas
"""
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel, Field, validator
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class PortfolioType(str, Enum):
    """Portfolio types."""
    GROWTH = "growth"
    INCOME = "income"
    BALANCED = "balanced"
    CONSERVATIVE = "conservative"
    AGGRESSIVE = "aggressive"

class AssetClass(str, Enum):
    """Asset classes."""
    EQUITY = "equity"
    BOND = "bond"
    COMMODITY = "commodity"
    REAL_ESTATE = "real_estate"
    CASH = "cash"
    CRYPTO = "crypto"

class TransactionType(str, Enum):
    """Transaction types."""
    BUY = "buy"
    SELL = "sell"
    DIVIDEND = "dividend"
    SPLIT = "split"
    REBALANCE = "rebalance"

# SQLAlchemy Models
class Portfolio(Base):
    """Portfolio database model."""
    __tablename__ = "portfolios"
    
    id = Column(String, primary_key=True)
    user_id = Column(String, nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    portfolio_type = Column(String, nullable=False)
    total_value = Column(Float, default=0.0)
    cash_balance = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    holdings = relationship("Holding", back_populates="portfolio")
    transactions = relationship("Transaction", back_populates="portfolio")

class Holding(Base):
    """Portfolio holding database model."""
    __tablename__ = "holdings"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    portfolio_id = Column(String, ForeignKey("portfolios.id"))
    symbol = Column(String, nullable=False)
    quantity = Column(Float, nullable=False)
    average_cost = Column(Float, nullable=False)
    current_price = Column(Float, default=0.0)
    market_value = Column(Float, default=0.0)
    asset_class = Column(String, nullable=False)
    sector = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="holdings")

class Transaction(Base):
    """Transaction database model."""
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    portfolio_id = Column(String, ForeignKey("portfolios.id"))
    symbol = Column(String, nullable=False)
    transaction_type = Column(String, nullable=False)
    quantity = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False)
    fees = Column(Float, default=0.0)
    transaction_date = Column(DateTime, default=datetime.utcnow)
    notes = Column(Text)
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="transactions")

# Pydantic Models for API
class PortfolioCreate(BaseModel):
    """Portfolio creation model."""
    user_id: str
    name: str
    description: Optional[str] = None
    portfolio_type: PortfolioType
    initial_cash: float = Field(default=0.0, ge=0)

class PortfolioUpdate(BaseModel):
    """Portfolio update model."""
    name: Optional[str] = None
    description: Optional[str] = None
    portfolio_type: Optional[PortfolioType] = None

class HoldingCreate(BaseModel):
    """Holding creation model."""
    symbol: str = Field(..., min_length=1, max_length=10)
    quantity: float = Field(..., gt=0)
    average_cost: float = Field(..., gt=0)
    asset_class: AssetClass
    sector: Optional[str] = None

class TransactionCreate(BaseModel):
    """Transaction creation model."""
    symbol: str = Field(..., min_length=1, max_length=10)
    transaction_type: TransactionType
    quantity: float = Field(..., gt=0)
    price: float = Field(..., gt=0)
    fees: float = Field(default=0.0, ge=0)
    notes: Optional[str] = None

class PortfolioSummary(BaseModel):
    """Portfolio summary model."""
    id: str
    name: str
    portfolio_type: str
    total_value: float
    cash_balance: float
    total_return: float
    total_return_pct: float
    day_change: float
    day_change_pct: float
    holdings_count: int
    last_updated: datetime

class HoldingDetail(BaseModel):
    """Detailed holding information."""
    symbol: str
    quantity: float
    average_cost: float
    current_price: float
    market_value: float
    unrealized_gain_loss: float
    unrealized_gain_loss_pct: float
    weight: float
    asset_class: str
    sector: Optional[str]

class PortfolioAnalytics(BaseModel):
    """Portfolio analytics model."""
    total_value: float
    total_return: float
    total_return_pct: float
    sharpe_ratio: Optional[float]
    volatility: Optional[float]
    beta: Optional[float]
    alpha: Optional[float]
    max_drawdown: Optional[float]
    asset_allocation: Dict[str, float]
    sector_allocation: Dict[str, float]
    top_holdings: List[HoldingDetail]
