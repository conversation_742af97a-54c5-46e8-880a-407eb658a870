"""
Database configuration and session management
"""
import logging
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from config.settings import settings
from core.portfolio.models import Base

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database manager for the financial AI system."""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection and tables."""
        try:
            # Create engine
            self.engine = create_engine(
                settings.DATABASE_URL,
                poolclass=StaticPool,
                connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {},
                echo=settings.DEBUG
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get database session."""
        return self.SessionLocal()
    
    def close_session(self, session: Session):
        """Close database session."""
        session.close()

# Global database manager
db_manager = DatabaseManager()

def get_db_session() -> Session:
    """Get database session (dependency injection)."""
    return db_manager.get_session()

def get_db():
    """Database dependency for FastAPI."""
    db = get_db_session()
    try:
        yield db
    finally:
        db.close()
