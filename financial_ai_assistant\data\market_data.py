"""
Market data integration with external APIs
"""
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import yfinance as yf
from alpha_vantage.timeseries import TimeSeries
from polygon import RESTClient

from config.settings import settings

logger = logging.getLogger(__name__)

class MarketDataProvider:
    """Base class for market data providers."""
    
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get current quote for symbol."""
        raise NotImplementedError
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """Get historical data for symbol."""
        raise NotImplementedError

class YFinanceProvider(MarketDataProvider):
    """Yahoo Finance data provider."""
    
    def __init__(self):
        logger.info("YFinance provider initialized")
    
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get current quote from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                "symbol": symbol,
                "price": info.get("currentPrice", 0),
                "change": info.get("regularMarketChange", 0),
                "change_percent": info.get("regularMarketChangePercent", 0),
                "volume": info.get("volume", 0),
                "market_cap": info.get("marketCap", 0),
                "pe_ratio": info.get("trailingPE", 0),
                "dividend_yield": info.get("dividendYield", 0),
                "timestamp": datetime.now(),
                "source": "yahoo_finance"
            }
        except Exception as e:
            logger.error(f"Error getting quote for {symbol} from Yahoo Finance: {e}")
            return {}
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """Get historical data from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(
                start=start_date,
                end=end_date,
                interval=interval
            )
            
            # Standardize column names
            data.columns = [col.lower().replace(" ", "_") for col in data.columns]
            data.reset_index(inplace=True)
            data["symbol"] = symbol
            data["source"] = "yahoo_finance"
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol} from Yahoo Finance: {e}")
            return pd.DataFrame()

class AlphaVantageProvider(MarketDataProvider):
    """Alpha Vantage data provider."""
    
    def __init__(self):
        if not settings.ALPHA_VANTAGE_API_KEY:
            logger.warning("Alpha Vantage API key not configured")
            self.client = None
        else:
            self.client = TimeSeries(key=settings.ALPHA_VANTAGE_API_KEY, output_format='pandas')
            logger.info("Alpha Vantage provider initialized")
    
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get current quote from Alpha Vantage."""
        if not self.client:
            return {}
        
        try:
            data, meta_data = self.client.get_quote_endpoint(symbol)
            
            return {
                "symbol": symbol,
                "price": float(data["05. price"]),
                "change": float(data["09. change"]),
                "change_percent": data["10. change percent"].replace("%", ""),
                "volume": int(data["06. volume"]),
                "timestamp": datetime.now(),
                "source": "alpha_vantage"
            }
        except Exception as e:
            logger.error(f"Error getting quote for {symbol} from Alpha Vantage: {e}")
            return {}
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """Get historical data from Alpha Vantage."""
        if not self.client:
            return pd.DataFrame()
        
        try:
            if interval == "1d":
                data, meta_data = self.client.get_daily_adjusted(symbol, outputsize='full')
            elif interval == "1wk":
                data, meta_data = self.client.get_weekly_adjusted(symbol)
            elif interval == "1mo":
                data, meta_data = self.client.get_monthly_adjusted(symbol)
            else:
                data, meta_data = self.client.get_intraday(symbol, interval=interval, outputsize='full')
            
            # Filter by date range
            data = data[(data.index >= start_date) & (data.index <= end_date)]
            
            # Standardize column names
            data.columns = [col.split('. ')[1].lower().replace(" ", "_") for col in data.columns]
            data.reset_index(inplace=True)
            data["symbol"] = symbol
            data["source"] = "alpha_vantage"
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol} from Alpha Vantage: {e}")
            return pd.DataFrame()

class PolygonProvider(MarketDataProvider):
    """Polygon.io data provider."""
    
    def __init__(self):
        if not settings.POLYGON_API_KEY:
            logger.warning("Polygon API key not configured")
            self.client = None
        else:
            self.client = RESTClient(api_key=settings.POLYGON_API_KEY)
            logger.info("Polygon provider initialized")
    
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get current quote from Polygon."""
        if not self.client:
            return {}
        
        try:
            quote = self.client.get_last_quote(symbol)
            
            return {
                "symbol": symbol,
                "price": (quote.bid + quote.ask) / 2,  # Mid price
                "bid": quote.bid,
                "ask": quote.ask,
                "bid_size": quote.bid_size,
                "ask_size": quote.ask_size,
                "timestamp": datetime.fromtimestamp(quote.timestamp / 1000),
                "source": "polygon"
            }
        except Exception as e:
            logger.error(f"Error getting quote for {symbol} from Polygon: {e}")
            return {}
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> pd.DataFrame:
        """Get historical data from Polygon."""
        if not self.client:
            return pd.DataFrame()
        
        try:
            # Convert interval to Polygon format
            timespan_map = {
                "1m": "minute",
                "5m": "minute",
                "15m": "minute",
                "30m": "minute",
                "1h": "hour",
                "1d": "day",
                "1wk": "week",
                "1mo": "month"
            }
            
            multiplier_map = {
                "1m": 1,
                "5m": 5,
                "15m": 15,
                "30m": 30,
                "1h": 1,
                "1d": 1,
                "1wk": 1,
                "1mo": 1
            }
            
            timespan = timespan_map.get(interval, "day")
            multiplier = multiplier_map.get(interval, 1)
            
            bars = self.client.get_aggs(
                ticker=symbol,
                multiplier=multiplier,
                timespan=timespan,
                from_=start_date.strftime("%Y-%m-%d"),
                to=end_date.strftime("%Y-%m-%d")
            )
            
            # Convert to DataFrame
            data = []
            for bar in bars:
                data.append({
                    "timestamp": datetime.fromtimestamp(bar.timestamp / 1000),
                    "open": bar.open,
                    "high": bar.high,
                    "low": bar.low,
                    "close": bar.close,
                    "volume": bar.volume,
                    "symbol": symbol,
                    "source": "polygon"
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol} from Polygon: {e}")
            return pd.DataFrame()

class MarketDataManager:
    """Manager for multiple market data providers."""
    
    def __init__(self):
        self.providers = {
            "yahoo": YFinanceProvider(),
            "alpha_vantage": AlphaVantageProvider(),
            "polygon": PolygonProvider()
        }
        self.default_provider = "yahoo"
        logger.info("Market Data Manager initialized")
    
    async def get_quote(self, symbol: str, provider: str = None) -> Dict[str, Any]:
        """Get quote from specified or default provider."""
        provider = provider or self.default_provider
        
        if provider not in self.providers:
            logger.warning(f"Provider {provider} not available, using default")
            provider = self.default_provider
        
        return await self.providers[provider].get_quote(symbol)
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d",
        provider: str = None
    ) -> pd.DataFrame:
        """Get historical data from specified or default provider."""
        provider = provider or self.default_provider
        
        if provider not in self.providers:
            logger.warning(f"Provider {provider} not available, using default")
            provider = self.default_provider
        
        return await self.providers[provider].get_historical_data(
            symbol, start_date, end_date, interval
        )
    
    async def get_multiple_quotes(self, symbols: List[str], provider: str = None) -> Dict[str, Dict[str, Any]]:
        """Get quotes for multiple symbols."""
        tasks = [self.get_quote(symbol, provider) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        quotes = {}
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"Error getting quote for {symbol}: {result}")
                quotes[symbol] = {}
            else:
                quotes[symbol] = result
        
        return quotes

# Global market data manager
market_data_manager = MarketDataManager()
