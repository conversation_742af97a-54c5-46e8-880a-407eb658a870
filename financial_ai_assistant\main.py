"""
Main entry point for Financial AI Assistant
Starts both the MCP server and Streamlit UI
"""
import asyncio
import logging
import subprocess
import sys
import time
from pathlib import Path
from threading import Thread

from config.settings import settings
from core.mcp_server.server import mcp_server

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def start_mcp_server():
    """Start the MCP server in a separate thread."""
    try:
        logger.info("Starting MCP Server...")
        mcp_server.run()
    except Exception as e:
        logger.error(f"Error starting MCP server: {e}")

def start_streamlit_ui():
    """Start the Streamlit UI."""
    try:
        logger.info("Starting Streamlit UI...")
        ui_path = Path(__file__).parent / "ui" / "main.py"
        
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            str(ui_path),
            "--server.port", str(settings.STREAMLIT_PORT),
            "--server.address", settings.HOST,
            "--server.headless", "true" if not settings.DEBUG else "false"
        ]
        
        subprocess.run(cmd)
        
    except Exception as e:
        logger.error(f"Error starting Streamlit UI: {e}")

def main():
    """Main function to start the complete system."""
    logger.info("🚀 Starting Financial AI Assistant")
    logger.info(f"📊 Version: {settings.APP_VERSION}")
    logger.info(f"🌐 MCP Server: {settings.HOST}:{settings.PORT}")
    logger.info(f"🖥️  Streamlit UI: {settings.HOST}:{settings.STREAMLIT_PORT}")
    
    try:
        # Start MCP server in background thread
        server_thread = Thread(target=start_mcp_server, daemon=True)
        server_thread.start()
        
        # Give server time to start
        time.sleep(2)
        
        # Start Streamlit UI (blocking)
        start_streamlit_ui()
        
    except KeyboardInterrupt:
        logger.info("Shutting down Financial AI Assistant...")
    except Exception as e:
        logger.error(f"Error running application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
