"""
Quick test to validate basic system functionality
"""
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

print("🚀 Financial AI Assistant - Quick System Test")
print("=" * 60)

# Test 1: Basic imports
print("\n📦 Testing Basic Imports...")
try:
    from config.settings import settings
    print("   ✅ Settings imported successfully")
    print(f"   📁 Project root: {settings.BASE_DIR}")
    print(f"   📁 Models dir: {settings.MODELS_DIR}")
except Exception as e:
    print(f"   ❌ Settings import failed: {e}")

try:
    from core.ml.predictor import ml_predictor
    print("   ✅ ML Predictor imported successfully")
except Exception as e:
    print(f"   ❌ ML Predictor import failed: {e}")

try:
    from core.llm.manager import FinancialLLMManager
    print("   ✅ LLM Manager imported successfully")
except Exception as e:
    print(f"   ❌ LLM Manager import failed: {e}")

# Test 2: Directory structure
print("\n📁 Testing Directory Structure...")
required_dirs = [
    settings.BASE_DIR,
    settings.DATA_DIR,
    settings.MODELS_DIR,
    settings.STORAGE_DIR
]

for dir_path in required_dirs:
    if dir_path.exists():
        print(f"   ✅ {dir_path.name}: {dir_path}")
    else:
        print(f"   ❌ Missing: {dir_path}")
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"   🔧 Created: {dir_path}")

# Test 3: Basic ML functionality
print("\n🤖 Testing Basic ML Functionality...")
try:
    import pandas as pd
    import numpy as np
    from sklearn.ensemble import RandomForestRegressor
    
    # Create sample data
    np.random.seed(42)
    X = np.random.randn(100, 5)
    y = np.random.randn(100)
    
    # Test basic ML model
    model = RandomForestRegressor(n_estimators=10, random_state=42)
    model.fit(X, y)
    predictions = model.predict(X[:5])
    
    print(f"   ✅ Basic ML model working")
    print(f"   📊 Sample predictions: {predictions[:3]}")
    
    # Test model persistence
    import joblib
    model_path = settings.MODELS_DIR / "test_model.pkl"
    joblib.dump(model, model_path)
    loaded_model = joblib.load(model_path)
    
    print(f"   ✅ Model persistence working")
    print(f"   💾 Model saved/loaded: {model_path}")
    
except Exception as e:
    print(f"   ❌ ML functionality error: {e}")

# Test 4: Data handling
print("\n📊 Testing Data Handling...")
try:
    import yfinance as yf
    
    # Test data fetch
    ticker = yf.Ticker("AAPL")
    hist = ticker.history(period="5d")
    
    if not hist.empty:
        print(f"   ✅ Data fetch successful")
        print(f"   📈 AAPL data: {len(hist)} records")
        print(f"   💰 Latest close: ${hist['Close'].iloc[-1]:.2f}")
    else:
        print(f"   ❌ No data retrieved")
        
except Exception as e:
    print(f"   ❌ Data handling error: {e}")

# Test 5: Feature engineering
print("\n🔧 Testing Feature Engineering...")
try:
    import talib
    
    # Test with sample data
    prices = np.random.randn(50) * 10 + 100  # Random prices around 100
    prices = np.cumsum(prices * 0.01) + 100  # Make it more realistic
    
    # Test technical indicators
    sma = talib.SMA(prices, timeperiod=10)
    rsi = talib.RSI(prices, timeperiod=14)
    
    print(f"   ✅ Technical indicators working")
    print(f"   📊 SMA sample: {sma[-1]:.2f}")
    print(f"   📊 RSI sample: {rsi[-1]:.2f}")
    
except Exception as e:
    print(f"   ❌ Feature engineering error: {e}")

# Test 6: System readiness
print("\n🎯 System Readiness Assessment...")

# Check required packages
required_packages = [
    'pandas', 'numpy', 'scikit-learn', 'xgboost', 'lightgbm', 
    'catboost', 'yfinance', 'talib', 'joblib', 'fastapi', 
    'streamlit', 'langchain', 'pydantic'
]

missing_packages = []
for package in required_packages:
    try:
        __import__(package)
        print(f"   ✅ {package}")
    except ImportError:
        print(f"   ❌ {package} - MISSING")
        missing_packages.append(package)

if missing_packages:
    print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
    print(f"   Install with: pip install {' '.join(missing_packages)}")
else:
    print(f"\n🎉 All required packages available!")

# Final assessment
print("\n" + "=" * 60)
print("📋 QUICK TEST SUMMARY")
print("=" * 60)

if len(missing_packages) == 0:
    print("✅ SYSTEM READY - All basic components functional")
    print("🚀 You can now run the complete ML system test")
    print("   Command: python run_complete_test.py")
else:
    print("⚠️  SYSTEM NEEDS SETUP - Install missing packages first")
    print(f"   Missing: {len(missing_packages)} packages")

print("=" * 60)
