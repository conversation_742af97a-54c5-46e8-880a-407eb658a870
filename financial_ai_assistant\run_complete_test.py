"""
Complete Financial AI System Test Runner
Tests ML models, PKL integration, LLM, MCP server, and achieves 95-99% accuracy
"""
import asyncio
import logging
import sys
import os
from pathlib import Path
import time
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Set environment variables for testing
os.environ.setdefault('OPENAI_API_KEY', 'test-key')
os.environ.setdefault('DATABASE_URL', 'sqlite:///./data/storage/financial_ai.db')

from test_ml_system import ml_tester
from core.ml.predictor import ml_predictor
from core.llm.manager import FinancialLLMManager
from data.market_data import market_data_manager
from config.settings import settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteSystemTester:
    """Complete system testing with integration validation."""
    
    def __init__(self):
        self.test_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA']
        self.results = {}
        self.start_time = None
        
    async def run_complete_system_test(self):
        """Run complete system test with all components."""
        self.start_time = time.time()
        
        print("\n" + "="*100)
        print("🚀 FINANCIAL AI ASSISTANT - COMPLETE SYSTEM TEST")
        print("   Testing ML Models, PKL Integration, LLM, MCP Server & Accuracy")
        print("="*100)
        
        try:
            # Phase 1: System Initialization
            await self._test_system_initialization()
            
            # Phase 2: Data Pipeline
            await self._test_data_pipeline()
            
            # Phase 3: ML Model Training & PKL Integration
            await self._test_ml_training_pkl()
            
            # Phase 4: LLM Integration
            await self._test_llm_ml_integration()
            
            # Phase 5: MCP Server Testing
            await self._test_mcp_server()
            
            # Phase 6: End-to-End Integration
            await self._test_end_to_end_integration()
            
            # Phase 7: Accuracy Validation
            await self._validate_accuracy_targets()
            
            # Final Report
            await self._generate_final_report()
            
        except Exception as e:
            print(f"\n❌ CRITICAL ERROR: {e}")
            logger.error(f"Complete system test failed: {e}")
        
        finally:
            elapsed = time.time() - self.start_time
            print(f"\n⏱️  Total test time: {elapsed:.1f} seconds")
            print("="*100)
    
    async def _test_system_initialization(self):
        """Test system initialization and configuration."""
        print("\n🔧 Phase 1: System Initialization")
        
        try:
            # Test directory structure
            required_dirs = [
                settings.PROJECT_ROOT / "data" / "storage",
                settings.MODELS_DIR,
                settings.PROJECT_ROOT / "logs"
            ]
            
            for dir_path in required_dirs:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"   ✅ Directory: {dir_path}")
            
            # Test configuration
            print(f"   ✅ Project root: {settings.PROJECT_ROOT}")
            print(f"   ✅ Models directory: {settings.MODELS_DIR}")
            
            self.results['initialization'] = {'status': 'success'}
            
        except Exception as e:
            print(f"   ❌ Initialization failed: {e}")
            self.results['initialization'] = {'status': 'failed', 'error': str(e)}
    
    async def _test_data_pipeline(self):
        """Test data pipeline and market data integration."""
        print("\n📊 Phase 2: Data Pipeline Testing")
        
        try:
            # Test market data manager
            test_symbol = self.test_symbols[0]
            
            # Test data fetching
            print(f"   Testing data fetch for {test_symbol}...")
            
            # Use Yahoo Finance as fallback for testing
            import yfinance as yf
            ticker = yf.Ticker(test_symbol)
            hist = ticker.history(period="1mo")
            
            if not hist.empty:
                print(f"   ✅ Data fetch successful: {len(hist)} records")
                self.results['data_pipeline'] = {
                    'status': 'success',
                    'records': len(hist),
                    'symbol': test_symbol
                }
            else:
                print(f"   ❌ No data retrieved for {test_symbol}")
                self.results['data_pipeline'] = {'status': 'failed', 'reason': 'no_data'}
            
        except Exception as e:
            print(f"   ❌ Data pipeline error: {e}")
            self.results['data_pipeline'] = {'status': 'failed', 'error': str(e)}
    
    async def _test_ml_training_pkl(self):
        """Test ML model training and PKL integration."""
        print("\n🤖 Phase 3: ML Training & PKL Integration")
        
        try:
            # Test with one symbol for speed
            test_symbol = self.test_symbols[0]
            print(f"   Training ML models for {test_symbol}...")
            
            # Train models
            results = await ml_predictor.train_high_accuracy_models(test_symbol, target_days=5)
            
            if results:
                best_accuracy = max([r.get('price_accuracy', 0) for r in results.values()])
                print(f"   ✅ Training completed - Best accuracy: {best_accuracy:.3f}")
                
                # Test PKL saving/loading
                await ml_predictor._save_models_pkl(test_symbol, results)
                loaded = await ml_predictor.load_models_pkl(test_symbol)
                
                if loaded:
                    print(f"   ✅ PKL integration successful")
                    self.results['ml_training'] = {
                        'status': 'success',
                        'best_accuracy': best_accuracy,
                        'pkl_integration': True,
                        'models_trained': len(results)
                    }
                else:
                    print(f"   ❌ PKL loading failed")
                    self.results['ml_training'] = {
                        'status': 'partial',
                        'best_accuracy': best_accuracy,
                        'pkl_integration': False
                    }
            else:
                print(f"   ❌ ML training failed")
                self.results['ml_training'] = {'status': 'failed'}
                
        except Exception as e:
            print(f"   ❌ ML training error: {e}")
            self.results['ml_training'] = {'status': 'failed', 'error': str(e)}
    
    async def _test_llm_ml_integration(self):
        """Test LLM integration with ML predictions."""
        print("\n🧠 Phase 4: LLM-ML Integration")
        
        try:
            # Initialize LLM manager
            llm_manager = FinancialLLMManager()
            
            if not llm_manager.providers:
                print("   ⚠️  No LLM providers configured - using mock response")
                self.results['llm_integration'] = {
                    'status': 'skipped',
                    'reason': 'no_providers'
                }
                return
            
            # Get ML prediction
            test_symbol = self.test_symbols[0]
            prediction = await ml_predictor.predict_with_ensemble(test_symbol)
            
            if prediction:
                # Test LLM with ML context
                response = await llm_manager.get_financial_advice_with_ml(
                    f"Analyze the ML prediction for {test_symbol}",
                    ml_predictions=prediction
                )
                
                if response and len(response) > 50:
                    print(f"   ✅ LLM-ML integration successful")
                    print(f"   📝 Response length: {len(response)} characters")
                    
                    self.results['llm_integration'] = {
                        'status': 'success',
                        'response_length': len(response),
                        'has_ml_context': 'prediction' in response.lower()
                    }
                else:
                    print(f"   ❌ LLM response insufficient")
                    self.results['llm_integration'] = {'status': 'failed', 'reason': 'poor_response'}
            else:
                print(f"   ❌ No ML prediction for LLM test")
                self.results['llm_integration'] = {'status': 'failed', 'reason': 'no_prediction'}
                
        except Exception as e:
            print(f"   ❌ LLM integration error: {e}")
            self.results['llm_integration'] = {'status': 'failed', 'error': str(e)}
    
    async def _test_mcp_server(self):
        """Test MCP server functionality."""
        print("\n🔗 Phase 5: MCP Server Testing")
        
        try:
            from fastapi.testclient import TestClient
            from core.mcp_server.server import app
            
            client = TestClient(app)
            
            # Test health endpoint
            response = client.get("/health")
            health_ok = response.status_code == 200
            
            # Test market data endpoint
            test_symbol = self.test_symbols[0]
            response = client.get(f"/market-data/{test_symbol}")
            market_data_ok = response.status_code == 200
            
            if health_ok and market_data_ok:
                print(f"   ✅ MCP server endpoints working")
                self.results['mcp_server'] = {
                    'status': 'success',
                    'health_check': health_ok,
                    'market_data': market_data_ok
                }
            else:
                print(f"   ❌ MCP server issues - Health: {health_ok}, Market: {market_data_ok}")
                self.results['mcp_server'] = {
                    'status': 'partial',
                    'health_check': health_ok,
                    'market_data': market_data_ok
                }
                
        except Exception as e:
            print(f"   ❌ MCP server error: {e}")
            self.results['mcp_server'] = {'status': 'failed', 'error': str(e)}
    
    async def _test_end_to_end_integration(self):
        """Test complete end-to-end integration."""
        print("\n🎯 Phase 6: End-to-End Integration")
        
        try:
            test_symbol = self.test_symbols[0]
            
            # Complete workflow test
            print(f"   Testing complete workflow for {test_symbol}...")
            
            # 1. Get ML prediction
            prediction = await ml_predictor.predict_with_ensemble(test_symbol)
            
            # 2. Get LLM analysis
            if prediction:
                llm_manager = FinancialLLMManager()
                if llm_manager.providers:
                    analysis = await llm_manager.get_financial_advice_with_ml(
                        f"Should I invest in {test_symbol}?",
                        ml_predictions=prediction
                    )
                else:
                    analysis = "Mock LLM analysis - providers not configured"
            else:
                analysis = None
            
            # 3. Validate complete workflow
            workflow_success = (
                prediction is not None and
                analysis is not None and
                len(analysis) > 50
            )
            
            if workflow_success:
                print(f"   ✅ End-to-end integration successful")
                self.results['e2e_integration'] = {
                    'status': 'success',
                    'has_prediction': prediction is not None,
                    'has_analysis': analysis is not None,
                    'workflow_complete': True
                }
            else:
                print(f"   ❌ End-to-end integration incomplete")
                self.results['e2e_integration'] = {
                    'status': 'failed',
                    'has_prediction': prediction is not None,
                    'has_analysis': analysis is not None,
                    'workflow_complete': False
                }
                
        except Exception as e:
            print(f"   ❌ E2E integration error: {e}")
            self.results['e2e_integration'] = {'status': 'failed', 'error': str(e)}

    async def _validate_accuracy_targets(self):
        """Validate accuracy targets and performance metrics."""
        print("\n📈 Phase 7: Accuracy Validation")

        try:
            # Get ML training results
            ml_results = self.results.get('ml_training', {})
            best_accuracy = ml_results.get('best_accuracy', 0)

            print(f"   Current best accuracy: {best_accuracy:.3f} ({best_accuracy*100:.1f}%)")

            # Accuracy targets
            targets = {
                'excellent': 0.99,  # 99%
                'good': 0.95,       # 95%
                'acceptable': 0.90  # 90%
            }

            accuracy_level = 'poor'
            if best_accuracy >= targets['excellent']:
                accuracy_level = 'excellent'
                print(f"   🎉 EXCELLENT: 99%+ accuracy achieved!")
            elif best_accuracy >= targets['good']:
                accuracy_level = 'good'
                print(f"   ✅ GOOD: 95%+ accuracy achieved!")
            elif best_accuracy >= targets['acceptable']:
                accuracy_level = 'acceptable'
                print(f"   ⚠️  ACCEPTABLE: 90%+ accuracy achieved")
            else:
                print(f"   ❌ POOR: Below 90% accuracy - needs improvement")

            # Performance metrics
            performance_score = self._calculate_performance_score()

            self.results['accuracy_validation'] = {
                'best_accuracy': best_accuracy,
                'accuracy_level': accuracy_level,
                'meets_95_target': best_accuracy >= 0.95,
                'meets_99_target': best_accuracy >= 0.99,
                'performance_score': performance_score
            }

            print(f"   📊 Overall performance score: {performance_score:.1f}/100")

        except Exception as e:
            print(f"   ❌ Accuracy validation error: {e}")
            self.results['accuracy_validation'] = {'status': 'failed', 'error': str(e)}

    def _calculate_performance_score(self) -> float:
        """Calculate overall system performance score."""
        try:
            score = 0.0
            max_score = 100.0

            # ML Training (40 points)
            ml_results = self.results.get('ml_training', {})
            if ml_results.get('status') == 'success':
                accuracy = ml_results.get('best_accuracy', 0)
                score += min(40, accuracy * 40)  # Up to 40 points for accuracy

            # PKL Integration (15 points)
            if ml_results.get('pkl_integration', False):
                score += 15

            # LLM Integration (15 points)
            llm_results = self.results.get('llm_integration', {})
            if llm_results.get('status') == 'success':
                score += 15

            # MCP Server (15 points)
            mcp_results = self.results.get('mcp_server', {})
            if mcp_results.get('status') == 'success':
                score += 15

            # E2E Integration (15 points)
            e2e_results = self.results.get('e2e_integration', {})
            if e2e_results.get('status') == 'success':
                score += 15

            return min(score, max_score)

        except Exception as e:
            logger.error(f"Error calculating performance score: {e}")
            return 0.0

    async def _generate_final_report(self):
        """Generate comprehensive final report."""
        print("\n📋 FINAL SYSTEM REPORT")
        print("-" * 50)

        try:
            # Summary statistics
            total_phases = len(self.results)
            successful_phases = sum(1 for r in self.results.values()
                                  if isinstance(r, dict) and r.get('status') == 'success')

            print(f"📊 Test Summary:")
            print(f"   • Total phases tested: {total_phases}")
            print(f"   • Successful phases: {successful_phases}")
            print(f"   • Success rate: {(successful_phases/total_phases*100):.1f}%")

            # Accuracy report
            accuracy_results = self.results.get('accuracy_validation', {})
            best_accuracy = accuracy_results.get('best_accuracy', 0)
            performance_score = accuracy_results.get('performance_score', 0)

            print(f"\n🎯 Accuracy & Performance:")
            print(f"   • Best ML accuracy: {best_accuracy:.3f} ({best_accuracy*100:.1f}%)")
            print(f"   • 95% target met: {'✅' if accuracy_results.get('meets_95_target', False) else '❌'}")
            print(f"   • 99% target met: {'✅' if accuracy_results.get('meets_99_target', False) else '❌'}")
            print(f"   • Performance score: {performance_score:.1f}/100")

            # Component status
            print(f"\n🔧 Component Status:")
            components = {
                'Data Pipeline': self.results.get('data_pipeline', {}).get('status', 'unknown'),
                'ML Training': self.results.get('ml_training', {}).get('status', 'unknown'),
                'PKL Integration': 'success' if self.results.get('ml_training', {}).get('pkl_integration', False) else 'failed',
                'LLM Integration': self.results.get('llm_integration', {}).get('status', 'unknown'),
                'MCP Server': self.results.get('mcp_server', {}).get('status', 'unknown'),
                'E2E Integration': self.results.get('e2e_integration', {}).get('status', 'unknown')
            }

            for component, status in components.items():
                status_icon = '✅' if status == 'success' else '❌' if status == 'failed' else '⚠️'
                print(f"   • {component}: {status_icon} {status}")

            # Recommendations
            print(f"\n💡 Recommendations:")
            if best_accuracy < 0.95:
                print(f"   • Increase training data and optimize hyperparameters")
                print(f"   • Add more technical indicators and features")

            if performance_score < 80:
                print(f"   • Address failed components above")
                print(f"   • Configure missing API keys and providers")

            # Overall assessment
            print(f"\n🏆 OVERALL ASSESSMENT:")
            if performance_score >= 90 and best_accuracy >= 0.95:
                print(f"   🎉 EXCELLENT - System ready for production!")
            elif performance_score >= 70 and best_accuracy >= 0.90:
                print(f"   ✅ GOOD - System functional with minor improvements needed")
            elif performance_score >= 50:
                print(f"   ⚠️  ACCEPTABLE - System needs optimization")
            else:
                print(f"   ❌ NEEDS WORK - Major issues require attention")

            # Save detailed report
            import json
            report_file = settings.PROJECT_ROOT / "complete_system_report.json"
            detailed_report = {
                'test_date': datetime.now().isoformat(),
                'test_duration': time.time() - self.start_time,
                'summary': {
                    'total_phases': total_phases,
                    'successful_phases': successful_phases,
                    'success_rate': successful_phases/total_phases,
                    'best_accuracy': best_accuracy,
                    'performance_score': performance_score
                },
                'detailed_results': self.results
            }

            with open(report_file, 'w') as f:
                json.dump(detailed_report, f, indent=2, default=str)

            print(f"\n💾 Detailed report saved to: {report_file}")

        except Exception as e:
            print(f"   ❌ Report generation error: {e}")
            logger.error(f"Final report generation failed: {e}")

# Global tester instance
complete_tester = CompleteSystemTester()

async def main():
    """Run the complete system test."""
    await complete_tester.run_complete_system_test()

if __name__ == "__main__":
    asyncio.run(main())
