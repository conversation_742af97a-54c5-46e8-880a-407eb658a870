"""
Test runner for Financial AI Assistant
Runs all tests and generates coverage reports
"""
import subprocess
import sys
import os
from pathlib import Path

def run_tests():
    """Run all tests with coverage."""
    print("🧪 Running Financial AI Assistant Test Suite")
    print("=" * 50)
    
    # Ensure we're in the right directory
    os.chdir(Path(__file__).parent)
    
    # Install test dependencies if needed
    print("📦 Installing test dependencies...")
    subprocess.run([
        sys.executable, "-m", "pip", "install", 
        "pytest", "pytest-asyncio", "pytest-cov", "httpx"
    ], check=True)
    
    # Run tests with coverage
    print("\n🔍 Running tests with coverage...")
    result = subprocess.run([
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--cov=core",
        "--cov=data",
        "--cov=config",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--asyncio-mode=auto"
    ])
    
    if result.returncode == 0:
        print("\n✅ All tests passed!")
        print("📊 Coverage report generated in htmlcov/")
    else:
        print("\n❌ Some tests failed!")
        return False
    
    return True

def run_integration_tests():
    """Run integration tests."""
    print("\n🔗 Running integration tests...")
    
    # Run system validation
    result = subprocess.run([sys.executable, "validate_system.py"])
    
    if result.returncode == 0:
        print("✅ Integration tests passed!")
        return True
    else:
        print("❌ Integration tests failed!")
        return False

def main():
    """Main test runner."""
    success = True
    
    # Run unit tests
    if not run_tests():
        success = False
    
    # Run integration tests
    if not run_integration_tests():
        success = False
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your Financial AI Assistant is ready for deployment!")
    else:
        print("\n💥 TESTS FAILED!")
        print("Please fix the issues before deploying.")
        sys.exit(1)

if __name__ == "__main__":
    main()
