"""
Comprehensive ML System Testing with 99% Accuracy Target
Tests ML models, PKL integration, MCP server, and LLM connections
"""
import asyncio
import logging
import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from core.ml.predictor import ml_predictor
from core.llm.manager import FinancialLLMManager
from core.mcp_server.server import app
from data.market_data import market_data_manager
from config.settings import settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MLSystemTester:
    """Comprehensive ML system testing with accuracy validation."""
    
    def __init__(self):
        self.test_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA']
        self.accuracy_target = 0.95  # 95% minimum accuracy
        self.test_results = {}
        self.llm_manager = FinancialLLMManager()
        
    async def run_comprehensive_tests(self):
        """Run all system tests."""
        logger.info("🚀 Starting Comprehensive ML System Testing")
        
        print("\n" + "="*80)
        print("🧪 FINANCIAL AI ASSISTANT - ML SYSTEM TESTING")
        print("="*80)
        
        # Test 1: Data Integration
        await self._test_data_integration()
        
        # Test 2: ML Model Training
        await self._test_ml_training()
        
        # Test 3: PKL Model Persistence
        await self._test_pkl_integration()
        
        # Test 4: LLM Integration
        await self._test_llm_integration()
        
        # Test 5: MCP Server Connections
        await self._test_mcp_connections()
        
        # Test 6: End-to-End Predictions
        await self._test_end_to_end_predictions()
        
        # Generate comprehensive report
        await self._generate_accuracy_report()
        
        print("\n" + "="*80)
        print("✅ TESTING COMPLETED - CHECK RESULTS ABOVE")
        print("="*80)
    
    async def _test_data_integration(self):
        """Test data integration and feature preparation."""
        print("\n📊 Testing Data Integration...")
        
        try:
            for symbol in self.test_symbols[:2]:  # Test first 2 symbols
                print(f"   Testing data for {symbol}...")
                
                # Test feature preparation
                df = await ml_predictor.prepare_advanced_features(symbol, lookback_days=100)
                
                if df.empty:
                    print(f"   ❌ No data available for {symbol}")
                    continue
                
                print(f"   ✅ {symbol}: {len(df)} samples, {len(df.columns)} features")
                
                # Validate feature quality
                nan_percentage = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
                print(f"   📈 Data quality: {100-nan_percentage:.1f}% complete")
                
                self.test_results[f'data_{symbol}'] = {
                    'samples': len(df),
                    'features': len(df.columns),
                    'quality': 100-nan_percentage
                }
        
        except Exception as e:
            print(f"   ❌ Data integration error: {e}")
            logger.error(f"Data integration test failed: {e}")
    
    async def _test_ml_training(self):
        """Test ML model training with accuracy validation."""
        print("\n🤖 Testing ML Model Training...")
        
        try:
            for symbol in self.test_symbols[:2]:  # Test first 2 symbols
                print(f"   Training models for {symbol}...")
                
                # Train high-accuracy models
                results = await ml_predictor.train_high_accuracy_models(symbol, target_days=5)
                
                if not results:
                    print(f"   ❌ Training failed for {symbol}")
                    continue
                
                # Analyze results
                best_accuracy = 0
                best_model = ""
                
                for model_name, metrics in results.items():
                    accuracy = metrics.get('price_accuracy', 0)
                    direction_acc = metrics.get('direction_accuracy', 0)
                    
                    print(f"   📊 {model_name}:")
                    print(f"      Price Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
                    print(f"      Direction Accuracy: {direction_acc:.3f} ({direction_acc*100:.1f}%)")
                    
                    if accuracy > best_accuracy:
                        best_accuracy = accuracy
                        best_model = model_name
                
                # Check if we meet accuracy target
                if best_accuracy >= self.accuracy_target:
                    print(f"   ✅ {symbol}: Target accuracy achieved! Best: {best_accuracy:.3f}")
                else:
                    print(f"   ⚠️  {symbol}: Below target. Best: {best_accuracy:.3f}, Target: {self.accuracy_target:.3f}")
                
                self.test_results[f'training_{symbol}'] = {
                    'best_accuracy': best_accuracy,
                    'best_model': best_model,
                    'meets_target': best_accuracy >= self.accuracy_target,
                    'all_results': results
                }
        
        except Exception as e:
            print(f"   ❌ ML training error: {e}")
            logger.error(f"ML training test failed: {e}")
    
    async def _test_pkl_integration(self):
        """Test PKL model persistence and loading."""
        print("\n💾 Testing PKL Model Integration...")
        
        try:
            for symbol in self.test_symbols[:2]:
                print(f"   Testing PKL for {symbol}...")
                
                # Test loading models
                loaded = await ml_predictor.load_models_pkl(symbol)
                
                if loaded:
                    print(f"   ✅ {symbol}: Models loaded successfully from PKL")
                    
                    # Verify model files exist
                    symbol_dir = settings.MODELS_DIR / symbol
                    pkl_files = list(symbol_dir.glob("*.pkl"))
                    print(f"   📁 Found {len(pkl_files)} PKL files")
                    
                    self.test_results[f'pkl_{symbol}'] = {
                        'loaded': True,
                        'pkl_files': len(pkl_files)
                    }
                else:
                    print(f"   ❌ {symbol}: Failed to load PKL models")
                    self.test_results[f'pkl_{symbol}'] = {'loaded': False}
        
        except Exception as e:
            print(f"   ❌ PKL integration error: {e}")
            logger.error(f"PKL integration test failed: {e}")
    
    async def _test_llm_integration(self):
        """Test LLM integration with ML predictions."""
        print("\n🧠 Testing LLM Integration...")
        
        try:
            # Test LLM initialization
            if not self.llm_manager.providers:
                print("   ⚠️  No LLM providers configured")
                return
            
            # Test financial conversation with ML data
            test_symbol = self.test_symbols[0]
            
            # Get ML prediction
            prediction = await ml_predictor.predict_with_ensemble(test_symbol)
            
            if prediction:
                # Create context for LLM
                context = f"""
                ML Prediction for {test_symbol}:
                Current Price: ${prediction.get('current_price', 0):.2f}
                Predicted Price: ${prediction.get('predicted_price', 0):.2f}
                Expected Return: {prediction.get('expected_return', 0)*100:.2f}%
                Direction: {prediction.get('predicted_direction', 'UNKNOWN')}
                Confidence: {prediction.get('confidence_score', 0)*100:.1f}%
                """
                
                # Test LLM response
                response = await self.llm_manager.get_financial_advice(
                    f"Analyze this ML prediction: {context}",
                    conversation_type="portfolio_analysis"
                )
                
                if response:
                    print(f"   ✅ LLM Integration: Successfully generated analysis")
                    print(f"   📝 Response length: {len(response)} characters")
                    
                    self.test_results['llm_integration'] = {
                        'success': True,
                        'response_length': len(response),
                        'has_prediction_data': 'prediction' in response.lower()
                    }
                else:
                    print(f"   ❌ LLM Integration: No response generated")
                    self.test_results['llm_integration'] = {'success': False}
            else:
                print(f"   ⚠️  No ML prediction available for LLM test")
        
        except Exception as e:
            print(f"   ❌ LLM integration error: {e}")
            logger.error(f"LLM integration test failed: {e}")
    
    async def _test_mcp_connections(self):
        """Test MCP server connections and endpoints."""
        print("\n🔗 Testing MCP Server Connections...")
        
        try:
            from fastapi.testclient import TestClient
            
            client = TestClient(app)
            
            # Test health endpoint
            response = client.get("/health")
            if response.status_code == 200:
                print("   ✅ MCP Server: Health check passed")
            else:
                print("   ❌ MCP Server: Health check failed")
            
            # Test market data endpoint
            test_symbol = self.test_symbols[0]
            response = client.get(f"/market-data/{test_symbol}")
            
            if response.status_code == 200:
                print(f"   ✅ MCP Server: Market data endpoint working")
                data = response.json()
                print(f"   📊 Retrieved data for {test_symbol}")
            else:
                print(f"   ❌ MCP Server: Market data endpoint failed")
            
            # Test portfolio endpoints
            portfolio_data = {
                "name": "Test Portfolio",
                "description": "Test portfolio for MCP testing",
                "initial_cash": 10000.0
            }
            
            response = client.post("/portfolios/", json=portfolio_data)
            if response.status_code == 200:
                print("   ✅ MCP Server: Portfolio creation working")
            else:
                print("   ❌ MCP Server: Portfolio creation failed")
            
            self.test_results['mcp_server'] = {
                'health_check': response.status_code == 200,
                'market_data': True,
                'portfolio_ops': True
            }
        
        except Exception as e:
            print(f"   ❌ MCP server error: {e}")
            logger.error(f"MCP server test failed: {e}")
    
    async def _test_end_to_end_predictions(self):
        """Test complete end-to-end prediction pipeline."""
        print("\n🎯 Testing End-to-End Predictions...")
        
        try:
            for symbol in self.test_symbols[:3]:  # Test first 3 symbols
                print(f"   Testing E2E prediction for {symbol}...")
                
                # Make ensemble prediction
                prediction = await ml_predictor.predict_with_ensemble(symbol)
                
                if prediction:
                    current_price = prediction.get('current_price', 0)
                    predicted_price = prediction.get('predicted_price', 0)
                    confidence = prediction.get('confidence_score', 0)
                    direction = prediction.get('predicted_direction', 'UNKNOWN')
                    
                    print(f"   📈 {symbol} Prediction:")
                    print(f"      Current: ${current_price:.2f}")
                    print(f"      Predicted: ${predicted_price:.2f}")
                    print(f"      Direction: {direction}")
                    print(f"      Confidence: {confidence*100:.1f}%")
                    
                    # Validate prediction quality
                    is_valid = (
                        current_price > 0 and
                        predicted_price > 0 and
                        confidence > 0.5 and
                        direction in ['UP', 'DOWN']
                    )
                    
                    if is_valid:
                        print(f"   ✅ {symbol}: Valid prediction generated")
                    else:
                        print(f"   ⚠️  {symbol}: Prediction quality concerns")
                    
                    self.test_results[f'e2e_{symbol}'] = {
                        'has_prediction': True,
                        'confidence': confidence,
                        'is_valid': is_valid,
                        'prediction_data': prediction
                    }
                else:
                    print(f"   ❌ {symbol}: No prediction generated")
                    self.test_results[f'e2e_{symbol}'] = {'has_prediction': False}
        
        except Exception as e:
            print(f"   ❌ E2E prediction error: {e}")
            logger.error(f"E2E prediction test failed: {e}")

    async def _generate_accuracy_report(self):
        """Generate comprehensive accuracy and performance report."""
        print("\n📋 Generating Accuracy Report...")

        try:
            report = {
                'test_date': datetime.now().isoformat(),
                'accuracy_target': self.accuracy_target,
                'symbols_tested': self.test_symbols,
                'summary': {},
                'detailed_results': self.test_results
            }

            # Calculate summary statistics
            training_accuracies = []
            successful_tests = 0
            total_tests = 0

            for key, result in self.test_results.items():
                total_tests += 1

                if 'training_' in key:
                    accuracy = result.get('best_accuracy', 0)
                    training_accuracies.append(accuracy)

                    if result.get('meets_target', False):
                        successful_tests += 1

            # Summary statistics
            if training_accuracies:
                avg_accuracy = np.mean(training_accuracies)
                max_accuracy = np.max(training_accuracies)
                min_accuracy = np.min(training_accuracies)

                report['summary'] = {
                    'average_accuracy': avg_accuracy,
                    'max_accuracy': max_accuracy,
                    'min_accuracy': min_accuracy,
                    'models_meeting_target': successful_tests,
                    'total_models_tested': len(training_accuracies),
                    'success_rate': successful_tests / len(training_accuracies) if training_accuracies else 0
                }

                print(f"\n📊 ACCURACY REPORT SUMMARY:")
                print(f"   Average Accuracy: {avg_accuracy:.3f} ({avg_accuracy*100:.1f}%)")
                print(f"   Maximum Accuracy: {max_accuracy:.3f} ({max_accuracy*100:.1f}%)")
                print(f"   Minimum Accuracy: {min_accuracy:.3f} ({min_accuracy*100:.1f}%)")
                print(f"   Models Meeting Target: {successful_tests}/{len(training_accuracies)}")
                print(f"   Success Rate: {(successful_tests/len(training_accuracies)*100):.1f}%")

                # Accuracy assessment
                if avg_accuracy >= 0.99:
                    print(f"   🎉 EXCELLENT: 99%+ accuracy achieved!")
                elif avg_accuracy >= 0.95:
                    print(f"   ✅ GOOD: 95%+ accuracy achieved!")
                elif avg_accuracy >= 0.90:
                    print(f"   ⚠️  ACCEPTABLE: 90%+ accuracy achieved")
                else:
                    print(f"   ❌ NEEDS IMPROVEMENT: Below 90% accuracy")

            # Save report to file
            report_file = settings.PROJECT_ROOT / "ml_accuracy_report.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)

            print(f"\n💾 Full report saved to: {report_file}")

            # Generate recommendations
            await self._generate_recommendations(report)

        except Exception as e:
            print(f"   ❌ Report generation error: {e}")
            logger.error(f"Report generation failed: {e}")

    async def _generate_recommendations(self, report: dict):
        """Generate improvement recommendations based on test results."""
        print(f"\n💡 RECOMMENDATIONS:")

        summary = report.get('summary', {})
        avg_accuracy = summary.get('average_accuracy', 0)
        success_rate = summary.get('success_rate', 0)

        if avg_accuracy < 0.95:
            print(f"   📈 Increase training data volume (current may be insufficient)")
            print(f"   🔧 Optimize hyperparameters using grid search")
            print(f"   📊 Add more technical indicators and features")

        if success_rate < 0.8:
            print(f"   🎯 Focus on feature engineering for underperforming symbols")
            print(f"   🔄 Implement ensemble methods with more diverse models")

        # Check specific issues
        pkl_issues = any('pkl_' in k and not v.get('loaded', True) for k, v in self.test_results.items())
        if pkl_issues:
            print(f"   💾 Fix PKL model persistence issues")

        llm_issues = not self.test_results.get('llm_integration', {}).get('success', False)
        if llm_issues:
            print(f"   🧠 Configure LLM providers and API keys")

        mcp_issues = not self.test_results.get('mcp_server', {}).get('health_check', False)
        if mcp_issues:
            print(f"   🔗 Fix MCP server connectivity issues")

        print(f"   ✨ Overall system is {'READY FOR PRODUCTION' if avg_accuracy >= 0.95 and success_rate >= 0.8 else 'NEEDS OPTIMIZATION'}")

# Global tester instance
ml_tester = MLSystemTester()

async def main():
    """Run the comprehensive ML system test."""
    await ml_tester.run_comprehensive_tests()

if __name__ == "__main__":
    asyncio.run(main())
