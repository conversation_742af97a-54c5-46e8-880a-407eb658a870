"""
Tests for MCP server functionality
"""
import pytest
import asyncio
from unittest.mock import Mock, patch
from fastapi.testclient import Test<PERSON><PERSON>

from core.mcp_server.server import FinancialMCPServer, FinancialDataRequest, PortfolioRequest

class TestFinancialMCPServer:
    """Test cases for Financial MCP Server."""
    
    @pytest.fixture
    def mcp_server(self):
        """Create MCP server instance for testing."""
        return FinancialMCPServer()
    
    @pytest.fixture
    def test_client(self, mcp_server):
        """Create test client for API testing."""
        return TestClient(mcp_server.app)
    
    def test_root_endpoint(self, test_client):
        """Test root endpoint."""
        response = test_client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "service" in data
        assert "Financial AI Assistant MCP Server" in data["service"]
    
    def test_health_check(self, test_client):
        """Test health check endpoint."""
        response = test_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "healthy" in data["message"]
    
    def test_market_data_endpoint(self, test_client):
        """Test market data endpoint."""
        request_data = {
            "symbol": "AAPL",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "interval": "1d"
        }
        
        with patch.object(FinancialMCPServer, '_fetch_market_data') as mock_fetch:
            mock_fetch.return_value = {"symbol": "AAPL", "data": []}
            
            response = test_client.post("/market-data", json=request_data)
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "AAPL" in data["message"]
    
    def test_create_portfolio_endpoint(self, test_client):
        """Test portfolio creation endpoint."""
        request_data = {
            "user_id": "test_user",
            "symbols": ["AAPL", "GOOGL"],
            "weights": [0.6, 0.4],
            "amount": 10000.0
        }
        
        with patch.object(FinancialMCPServer, '_create_portfolio') as mock_create:
            mock_create.return_value = "portfolio_123"
            
            response = test_client.post("/portfolio/create", json=request_data)
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["data"]["portfolio_id"] == "portfolio_123"
    
    def test_get_portfolio_endpoint(self, test_client):
        """Test portfolio retrieval endpoint."""
        portfolio_id = "test_portfolio_123"
        
        with patch.object(FinancialMCPServer, '_get_portfolio') as mock_get:
            mock_get.return_value = {
                "portfolio_id": portfolio_id,
                "holdings": [],
                "performance": {}
            }
            
            response = test_client.get(f"/portfolio/{portfolio_id}")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["data"]["portfolio_id"] == portfolio_id
    
    def test_analyze_portfolio_endpoint(self, test_client):
        """Test portfolio analysis endpoint."""
        portfolio_id = "test_portfolio_123"
        
        response = test_client.post(f"/portfolio/{portfolio_id}/analyze")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "analysis started" in data["message"]

class TestMCPModels:
    """Test cases for MCP data models."""
    
    def test_financial_data_request(self):
        """Test FinancialDataRequest model."""
        request = FinancialDataRequest(
            symbol="AAPL",
            start_date="2024-01-01",
            end_date="2024-12-31",
            interval="1d"
        )
        assert request.symbol == "AAPL"
        assert request.interval == "1d"
    
    def test_portfolio_request(self):
        """Test PortfolioRequest model."""
        request = PortfolioRequest(
            user_id="test_user",
            symbols=["AAPL", "GOOGL"],
            weights=[0.6, 0.4],
            amount=10000.0
        )
        assert request.user_id == "test_user"
        assert len(request.symbols) == 2
        assert sum(request.weights) == 1.0

if __name__ == "__main__":
    pytest.main([__file__])
