"""
Tests for portfolio management functionality
"""
import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

from core.portfolio.manager import PortfolioManager
from core.portfolio.models import (
    PortfolioCreate, PortfolioType, AssetClass, HoldingCreate, TransactionType
)

class TestPortfolioManager:
    """Test cases for PortfolioManager."""
    
    @pytest.fixture
    def portfolio_manager(self):
        """Create a portfolio manager instance for testing."""
        with patch('core.portfolio.manager.get_db_session'):
            return PortfolioManager()
    
    @pytest.fixture
    def sample_portfolio_data(self):
        """Sample portfolio creation data."""
        return PortfolioCreate(
            user_id="test_user",
            name="Test Portfolio",
            description="A test portfolio",
            portfolio_type=PortfolioType.BALANCED,
            initial_cash=10000.0
        )
    
    @pytest.fixture
    def sample_holding_data(self):
        """Sample holding creation data."""
        return HoldingCreate(
            symbol="AAPL",
            quantity=10.0,
            average_cost=150.0,
            asset_class=AssetClass.EQUITY,
            sector="Technology"
        )
    
    @pytest.mark.asyncio
    async def test_create_portfolio(self, portfolio_manager, sample_portfolio_data):
        """Test portfolio creation."""
        with patch.object(portfolio_manager.db_session, 'add'), \
             patch.object(portfolio_manager.db_session, 'commit'):
            
            portfolio_id = await portfolio_manager.create_portfolio(sample_portfolio_data)
            
            assert portfolio_id is not None
            assert portfolio_id.startswith("portfolio_test_user_")
    
    @pytest.mark.asyncio
    async def test_get_portfolio(self, portfolio_manager):
        """Test portfolio retrieval."""
        mock_portfolio = Mock()
        mock_portfolio.id = "test_portfolio_id"
        mock_portfolio.name = "Test Portfolio"
        
        with patch.object(portfolio_manager.db_session, 'query') as mock_query:
            mock_query.return_value.filter.return_value.first.return_value = mock_portfolio
            
            portfolio = await portfolio_manager.get_portfolio("test_portfolio_id")
            
            assert portfolio is not None
            assert portfolio.id == "test_portfolio_id"
            assert portfolio.name == "Test Portfolio"
    
    @pytest.mark.asyncio
    async def test_add_holding(self, portfolio_manager, sample_holding_data):
        """Test adding a holding to portfolio."""
        with patch.object(portfolio_manager.db_session, 'query') as mock_query, \
             patch.object(portfolio_manager.db_session, 'add'), \
             patch.object(portfolio_manager.db_session, 'commit'), \
             patch.object(portfolio_manager, 'get_portfolio') as mock_get_portfolio:
            
            # Mock no existing holding
            mock_query.return_value.filter.return_value.first.return_value = None
            
            # Mock portfolio
            mock_portfolio = Mock()
            mock_portfolio.cash_balance = 10000.0
            mock_get_portfolio.return_value = mock_portfolio
            
            result = await portfolio_manager.add_holding("test_portfolio", sample_holding_data)
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_get_portfolio_summary(self, portfolio_manager):
        """Test portfolio summary generation."""
        mock_portfolio = Mock()
        mock_portfolio.id = "test_portfolio"
        mock_portfolio.name = "Test Portfolio"
        mock_portfolio.portfolio_type = "balanced"
        mock_portfolio.cash_balance = 5000.0
        mock_portfolio.updated_at = datetime.now()
        
        mock_holdings = [
            Mock(market_value=1500.0, quantity=10.0, average_cost=150.0),
            Mock(market_value=2500.0, quantity=5.0, average_cost=500.0)
        ]
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio), \
             patch.object(portfolio_manager.db_session, 'query') as mock_query:
            
            mock_query.return_value.filter.return_value.all.return_value = mock_holdings
            
            summary = await portfolio_manager.get_portfolio_summary("test_portfolio")
            
            assert summary is not None
            assert summary.id == "test_portfolio"
            assert summary.name == "Test Portfolio"
            assert summary.total_value == 9000.0  # 4000 market value + 5000 cash
            assert summary.holdings_count == 2

class TestPortfolioModels:
    """Test cases for portfolio data models."""
    
    def test_portfolio_create_validation(self):
        """Test portfolio creation model validation."""
        # Valid data
        valid_data = PortfolioCreate(
            user_id="test_user",
            name="Test Portfolio",
            portfolio_type=PortfolioType.GROWTH,
            initial_cash=1000.0
        )
        assert valid_data.user_id == "test_user"
        assert valid_data.initial_cash == 1000.0
    
    def test_holding_create_validation(self):
        """Test holding creation model validation."""
        # Valid data
        valid_data = HoldingCreate(
            symbol="AAPL",
            quantity=10.0,
            average_cost=150.0,
            asset_class=AssetClass.EQUITY
        )
        assert valid_data.symbol == "AAPL"
        assert valid_data.quantity == 10.0
        
        # Invalid data - negative quantity
        with pytest.raises(ValueError):
            HoldingCreate(
                symbol="AAPL",
                quantity=-10.0,
                average_cost=150.0,
                asset_class=AssetClass.EQUITY
            )

if __name__ == "__main__":
    pytest.main([__file__])
