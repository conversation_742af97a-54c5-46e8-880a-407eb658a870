"""
Main Streamlit application for Financial AI Assistant
"""
import streamlit as st
import asyncio
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm.manager import llm_manager, ConversationType, LLMProvider
from core.portfolio.manager import PortfolioManager
from core.portfolio.models import PortfolioCreate, PortfolioType, AssetClass
from data.market_data import market_data_manager
from config.settings import settings

# Page configuration
st.set_page_config(
    page_title="Financial AI Assistant",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .ai-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if "user_id" not in st.session_state:
        st.session_state.user_id = "demo_user"
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []
    if "portfolio_manager" not in st.session_state:
        st.session_state.portfolio_manager = PortfolioManager()
    if "current_portfolio" not in st.session_state:
        st.session_state.current_portfolio = None

def sidebar():
    """Create sidebar with navigation and settings."""
    st.sidebar.title("🏦 Financial AI Assistant")
    
    # Navigation
    page = st.sidebar.selectbox(
        "Navigate",
        ["🏠 Dashboard", "💼 Portfolio", "💬 AI Chat", "📊 Market Data", "⚙️ Settings"]
    )
    
    # User info
    st.sidebar.markdown("---")
    st.sidebar.markdown(f"**User:** {st.session_state.user_id}")
    
    # Quick stats (placeholder)
    st.sidebar.markdown("---")
    st.sidebar.markdown("**Quick Stats**")
    st.sidebar.metric("Portfolio Value", "$0", "0%")
    st.sidebar.metric("Day Change", "$0", "0%")
    
    return page

def dashboard_page():
    """Main dashboard page."""
    st.markdown('<h1 class="main-header">📊 Financial Dashboard</h1>', unsafe_allow_html=True)
    
    # Key metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="Total Portfolio Value",
            value="$0",
            delta="0%"
        )
    
    with col2:
        st.metric(
            label="Today's Change",
            value="$0",
            delta="0%"
        )
    
    with col3:
        st.metric(
            label="Total Return",
            value="$0",
            delta="0%"
        )
    
    with col4:
        st.metric(
            label="Active Positions",
            value="0",
            delta="0"
        )
    
    # Charts row
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Portfolio Performance")
        # Placeholder chart
        dates = pd.date_range(start="2024-01-01", end="2024-12-31", freq="D")
        values = [10000 + i * 10 for i in range(len(dates))]
        df = pd.DataFrame({"Date": dates, "Value": values})
        
        fig = px.line(df, x="Date", y="Value", title="Portfolio Value Over Time")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Asset Allocation")
        # Placeholder pie chart
        labels = ["Stocks", "Bonds", "Cash", "Other"]
        values = [60, 25, 10, 5]
        
        fig = px.pie(values=values, names=labels, title="Current Asset Allocation")
        st.plotly_chart(fig, use_container_width=True)
    
    # Recent activity
    st.subheader("Recent Activity")
    st.info("No recent activity to display. Start by creating a portfolio or asking the AI assistant for help!")

def portfolio_page():
    """Portfolio management page."""
    st.markdown('<h1 class="main-header">💼 Portfolio Management</h1>', unsafe_allow_html=True)
    
    # Portfolio creation/selection
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Create New Portfolio")
        
        with st.form("create_portfolio"):
            portfolio_name = st.text_input("Portfolio Name", placeholder="My Investment Portfolio")
            portfolio_description = st.text_area("Description", placeholder="Portfolio description...")
            portfolio_type = st.selectbox("Portfolio Type", [e.value for e in PortfolioType])
            initial_cash = st.number_input("Initial Cash", min_value=0.0, value=10000.0, step=100.0)
            
            if st.form_submit_button("Create Portfolio"):
                if portfolio_name:
                    try:
                        portfolio_data = PortfolioCreate(
                            user_id=st.session_state.user_id,
                            name=portfolio_name,
                            description=portfolio_description,
                            portfolio_type=PortfolioType(portfolio_type),
                            initial_cash=initial_cash
                        )
                        
                        # This would be async in real implementation
                        # portfolio_id = await st.session_state.portfolio_manager.create_portfolio(portfolio_data)
                        st.success(f"Portfolio '{portfolio_name}' created successfully!")
                        
                    except Exception as e:
                        st.error(f"Error creating portfolio: {e}")
                else:
                    st.error("Please enter a portfolio name")
    
    with col2:
        st.subheader("Portfolio Actions")
        
        if st.button("📊 Analyze Portfolio"):
            st.info("Portfolio analysis feature coming soon!")
        
        if st.button("⚖️ Rebalance Portfolio"):
            st.info("Portfolio rebalancing feature coming soon!")
        
        if st.button("📈 Performance Report"):
            st.info("Performance reporting feature coming soon!")
    
    # Holdings table (placeholder)
    st.subheader("Current Holdings")
    
    # Placeholder data
    holdings_data = {
        "Symbol": ["AAPL", "GOOGL", "MSFT", "TSLA"],
        "Quantity": [10, 5, 15, 8],
        "Avg Cost": [150.00, 2500.00, 300.00, 800.00],
        "Current Price": [175.00, 2600.00, 350.00, 750.00],
        "Market Value": [1750.00, 13000.00, 5250.00, 6000.00],
        "Gain/Loss": [250.00, 500.00, 750.00, -400.00],
        "Gain/Loss %": [16.67, 4.00, 16.67, -6.25]
    }
    
    df = pd.DataFrame(holdings_data)
    st.dataframe(df, use_container_width=True)

def chat_page():
    """AI chat interface page."""
    st.markdown('<h1 class="main-header">💬 AI Financial Assistant</h1>', unsafe_allow_html=True)
    
    # Chat type selection
    col1, col2 = st.columns([3, 1])
    
    with col2:
        conversation_type = st.selectbox(
            "Conversation Type",
            [e.value for e in ConversationType],
            format_func=lambda x: x.replace("_", " ").title()
        )
        
        llm_provider = st.selectbox(
            "AI Provider",
            [e.value for e in LLMProvider],
            format_func=lambda x: x.upper()
        )
        
        if st.button("Clear Chat"):
            st.session_state.chat_history = []
            st.rerun()
    
    # Chat history
    chat_container = st.container()
    
    with chat_container:
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                st.markdown(
                    f'<div class="chat-message user-message"><strong>You:</strong> {message["content"]}</div>',
                    unsafe_allow_html=True
                )
            else:
                st.markdown(
                    f'<div class="chat-message ai-message"><strong>AI Assistant:</strong> {message["content"]}</div>',
                    unsafe_allow_html=True
                )
    
    # Chat input
    user_input = st.chat_input("Ask me anything about finance, investments, or your portfolio...")
    
    if user_input:
        # Add user message to history
        st.session_state.chat_history.append({"role": "user", "content": user_input})
        
        # Get AI response (placeholder)
        ai_response = f"Thank you for your question about: '{user_input}'. This is a placeholder response. The AI integration will provide detailed financial analysis and advice based on your query."
        
        # Add AI response to history
        st.session_state.chat_history.append({"role": "assistant", "content": ai_response})
        
        st.rerun()

def market_data_page():
    """Market data and analysis page."""
    st.markdown('<h1 class="main-header">📊 Market Data & Analysis</h1>', unsafe_allow_html=True)
    
    # Symbol input
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        symbol = st.text_input("Enter Stock Symbol", value="AAPL", placeholder="AAPL, GOOGL, MSFT...")
    
    with col2:
        if st.button("Get Quote"):
            st.info(f"Getting quote for {symbol}...")
    
    with col3:
        if st.button("Get Chart"):
            st.info(f"Loading chart for {symbol}...")
    
    # Market overview
    st.subheader("Market Overview")
    
    # Placeholder market data
    market_data = {
        "Index": ["S&P 500", "NASDAQ", "DOW", "Russell 2000"],
        "Value": [4500.00, 14000.00, 35000.00, 2000.00],
        "Change": [25.50, 100.25, 150.75, -10.25],
        "Change %": [0.57, 0.72, 0.43, -0.51]
    }
    
    df = pd.DataFrame(market_data)
    st.dataframe(df, use_container_width=True)
    
    # Stock chart (placeholder)
    st.subheader(f"{symbol} Price Chart")
    
    # Generate sample data
    dates = pd.date_range(start="2024-01-01", end="2024-12-31", freq="D")
    prices = [150 + i * 0.1 + (i % 10) * 2 for i in range(len(dates))]
    df_chart = pd.DataFrame({"Date": dates, "Price": prices})
    
    fig = px.line(df_chart, x="Date", y="Price", title=f"{symbol} Stock Price")
    st.plotly_chart(fig, use_container_width=True)

def settings_page():
    """Settings and configuration page."""
    st.markdown('<h1 class="main-header">⚙️ Settings</h1>', unsafe_allow_html=True)
    
    # API Configuration
    st.subheader("API Configuration")
    
    with st.expander("API Keys", expanded=False):
        st.text_input("OpenAI API Key", type="password", placeholder="sk-...")
        st.text_input("Anthropic API Key", type="password", placeholder="sk-ant-...")
        st.text_input("Polygon.io API Key", type="password", placeholder="Your Polygon API key")
        st.text_input("Alpha Vantage API Key", type="password", placeholder="Your Alpha Vantage API key")
        
        if st.button("Save API Keys"):
            st.success("API keys saved successfully!")
    
    # User Preferences
    st.subheader("User Preferences")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.selectbox("Default Currency", ["USD", "EUR", "GBP", "JPY"])
        st.selectbox("Risk Tolerance", ["Conservative", "Moderate", "Aggressive"])
        st.number_input("Investment Horizon (years)", min_value=1, max_value=50, value=10)
    
    with col2:
        st.selectbox("Preferred LLM", ["OpenAI GPT-4", "Anthropic Claude", "Local Model"])
        st.slider("AI Response Temperature", 0.0, 1.0, 0.1, 0.1)
        st.checkbox("Enable Real-time Notifications")
    
    # System Information
    st.subheader("System Information")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info(f"**Version:** {settings.APP_VERSION}")
        st.info(f"**Server:** {settings.MCP_SERVER_URL}")
    
    with col2:
        st.info(f"**Database:** Connected")
        st.info(f"**Market Data:** Available")

def main():
    """Main application function."""
    initialize_session_state()
    
    # Sidebar navigation
    page = sidebar()
    
    # Route to appropriate page
    if page == "🏠 Dashboard":
        dashboard_page()
    elif page == "💼 Portfolio":
        portfolio_page()
    elif page == "💬 AI Chat":
        chat_page()
    elif page == "📊 Market Data":
        market_data_page()
    elif page == "⚙️ Settings":
        settings_page()

if __name__ == "__main__":
    main()
