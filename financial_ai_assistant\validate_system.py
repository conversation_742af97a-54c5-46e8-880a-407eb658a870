"""
System validation script for Financial AI Assistant
Validates all components and their integration
"""
import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemValidator:
    """Comprehensive system validation."""
    
    def __init__(self):
        self.validation_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test result."""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: PASSED {message}")
        else:
            logger.error(f"❌ {test_name}: FAILED {message}")
        
        self.validation_results[test_name] = {
            "passed": passed,
            "message": message
        }
    
    def validate_directory_structure(self):
        """Validate project directory structure."""
        logger.info("🔍 Validating directory structure...")
        
        required_dirs = [
            "core",
            "core/mcp_server",
            "core/agents",
            "core/portfolio",
            "core/llm",
            "data",
            "ui",
            "tests",
            "config"
        ]
        
        for dir_path in required_dirs:
            path = Path(dir_path)
            self.log_test(
                f"Directory: {dir_path}",
                path.exists() and path.is_dir(),
                f"at {path.absolute()}"
            )
    
    def validate_core_files(self):
        """Validate core system files."""
        logger.info("📁 Validating core files...")
        
        required_files = [
            "main.py",
            "requirements.txt",
            "README.md",
            ".env.example",
            "config/settings.py",
            "core/mcp_server/server.py",
            "core/agents/base_agent.py",
            "core/agents/portfolio_agent.py",
            "core/portfolio/models.py",
            "core/portfolio/manager.py",
            "core/llm/manager.py",
            "data/database.py",
            "data/market_data.py",
            "ui/main.py"
        ]
        
        for file_path in required_files:
            path = Path(file_path)
            self.log_test(
                f"File: {file_path}",
                path.exists() and path.is_file(),
                f"at {path.absolute()}"
            )
    
    def validate_imports(self):
        """Validate that all modules can be imported."""
        logger.info("📦 Validating imports...")
        
        import_tests = [
            ("config.settings", "settings"),
            ("core.mcp_server.server", "FinancialMCPServer"),
            ("core.agents.base_agent", "BaseFinancialAgent"),
            ("core.agents.portfolio_agent", "PortfolioAgent"),
            ("core.portfolio.models", "Portfolio"),
            ("core.portfolio.manager", "PortfolioManager"),
            ("core.llm.manager", "FinancialLLMManager"),
            ("data.database", "DatabaseManager"),
            ("data.market_data", "MarketDataManager")
        ]
        
        for module_name, class_name in import_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                self.log_test(f"Import: {module_name}.{class_name}", True)
            except Exception as e:
                self.log_test(f"Import: {module_name}.{class_name}", False, str(e))
    
    def validate_configuration(self):
        """Validate configuration settings."""
        logger.info("⚙️ Validating configuration...")
        
        try:
            from config.settings import settings
            
            # Test basic settings
            self.log_test("Config: APP_NAME", hasattr(settings, 'APP_NAME'))
            self.log_test("Config: APP_VERSION", hasattr(settings, 'APP_VERSION'))
            self.log_test("Config: HOST", hasattr(settings, 'HOST'))
            self.log_test("Config: PORT", hasattr(settings, 'PORT'))
            self.log_test("Config: DATABASE_URL", hasattr(settings, 'DATABASE_URL'))
            
            # Test directory creation
            self.log_test("Config: DATA_DIR exists", settings.DATA_DIR.exists())
            self.log_test("Config: MODELS_DIR exists", settings.MODELS_DIR.exists())
            self.log_test("Config: STORAGE_DIR exists", settings.STORAGE_DIR.exists())
            
        except Exception as e:
            self.log_test("Configuration validation", False, str(e))
    
    def validate_database(self):
        """Validate database functionality."""
        logger.info("🗄️ Validating database...")
        
        try:
            from data.database import DatabaseManager
            from core.portfolio.models import Base
            
            # Test database manager creation
            db_manager = DatabaseManager()
            self.log_test("Database: Manager creation", True)
            
            # Test session creation
            session = db_manager.get_session()
            self.log_test("Database: Session creation", session is not None)
            
            # Test table creation (should already be done in __init__)
            self.log_test("Database: Tables created", True)
            
            session.close()
            
        except Exception as e:
            self.log_test("Database validation", False, str(e))
    
    def validate_mcp_server(self):
        """Validate MCP server functionality."""
        logger.info("🌐 Validating MCP server...")
        
        try:
            from core.mcp_server.server import FinancialMCPServer
            
            # Test server creation
            server = FinancialMCPServer()
            self.log_test("MCP Server: Creation", True)
            
            # Test FastAPI app
            self.log_test("MCP Server: FastAPI app", hasattr(server, 'app'))
            
            # Test routes setup
            routes = [route.path for route in server.app.routes]
            expected_routes = ["/", "/health", "/market-data", "/portfolio/create"]
            
            for route in expected_routes:
                self.log_test(f"MCP Server: Route {route}", route in routes)
            
        except Exception as e:
            self.log_test("MCP Server validation", False, str(e))
    
    def validate_agents(self):
        """Validate agent functionality."""
        logger.info("🤖 Validating agents...")
        
        try:
            from core.agents.portfolio_agent import PortfolioAgent
            
            # Test agent creation (may fail without API keys)
            try:
                agent = PortfolioAgent()
                self.log_test("Agents: Portfolio agent creation", True)
                
                # Test capabilities
                capabilities = agent.get_capabilities()
                self.log_test("Agents: Capabilities", isinstance(capabilities, dict))
                
            except Exception as e:
                self.log_test("Agents: Portfolio agent creation", False, f"API key required: {e}")
            
        except Exception as e:
            self.log_test("Agents validation", False, str(e))
    
    def validate_portfolio_system(self):
        """Validate portfolio management system."""
        logger.info("💼 Validating portfolio system...")
        
        try:
            from core.portfolio.manager import PortfolioManager
            from core.portfolio.models import PortfolioCreate, PortfolioType
            
            # Test portfolio manager creation
            manager = PortfolioManager()
            self.log_test("Portfolio: Manager creation", True)
            
            # Test model validation
            portfolio_data = PortfolioCreate(
                user_id="test_user",
                name="Test Portfolio",
                portfolio_type=PortfolioType.BALANCED,
                initial_cash=10000.0
            )
            self.log_test("Portfolio: Model validation", True)
            
        except Exception as e:
            self.log_test("Portfolio system validation", False, str(e))
    
    def validate_market_data(self):
        """Validate market data functionality."""
        logger.info("📊 Validating market data...")
        
        try:
            from data.market_data import MarketDataManager
            
            # Test market data manager creation
            manager = MarketDataManager()
            self.log_test("Market Data: Manager creation", True)
            
            # Test providers
            self.log_test("Market Data: Yahoo provider", "yahoo" in manager.providers)
            self.log_test("Market Data: Alpha Vantage provider", "alpha_vantage" in manager.providers)
            self.log_test("Market Data: Polygon provider", "polygon" in manager.providers)
            
        except Exception as e:
            self.log_test("Market data validation", False, str(e))
    
    def validate_llm_system(self):
        """Validate LLM system."""
        logger.info("🧠 Validating LLM system...")
        
        try:
            from core.llm.manager import FinancialLLMManager
            
            # Test LLM manager creation
            manager = FinancialLLMManager()
            self.log_test("LLM: Manager creation", True)
            
            # Test provider availability (depends on API keys)
            if manager.providers:
                self.log_test("LLM: Providers available", True)
            else:
                self.log_test("LLM: Providers available", False, "No API keys configured")
            
        except Exception as e:
            self.log_test("LLM system validation", False, str(e))
    
    def validate_ui_system(self):
        """Validate UI system."""
        logger.info("🖥️ Validating UI system...")
        
        try:
            # Test if Streamlit UI file exists and is valid Python
            ui_path = Path("ui/main.py")
            if ui_path.exists():
                with open(ui_path, 'r') as f:
                    content = f.read()
                
                # Basic validation
                self.log_test("UI: Main file exists", True)
                self.log_test("UI: Contains Streamlit imports", "import streamlit" in content)
                self.log_test("UI: Contains main function", "def main(" in content)
            else:
                self.log_test("UI: Main file exists", False)
            
        except Exception as e:
            self.log_test("UI system validation", False, str(e))
    
    def run_validation(self):
        """Run complete system validation."""
        logger.info("🚀 Starting Financial AI Assistant System Validation")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # Run all validation tests
        self.validate_directory_structure()
        self.validate_core_files()
        self.validate_imports()
        self.validate_configuration()
        self.validate_database()
        self.validate_mcp_server()
        self.validate_agents()
        self.validate_portfolio_system()
        self.validate_market_data()
        self.validate_llm_system()
        self.validate_ui_system()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Print summary
        logger.info("=" * 60)
        logger.info("🎯 VALIDATION SUMMARY")
        logger.info(f"⏱️  Duration: {duration:.2f} seconds")
        logger.info(f"📊 Tests: {self.passed_tests}/{self.total_tests} passed")
        logger.info(f"✅ Success Rate: {(self.passed_tests/self.total_tests*100):.1f}%")
        
        if self.passed_tests == self.total_tests:
            logger.info("🎉 ALL TESTS PASSED - SYSTEM READY!")
            return True
        else:
            logger.warning(f"⚠️  {self.total_tests - self.passed_tests} tests failed")
            return False

def main():
    """Main validation function."""
    validator = SystemValidator()
    success = validator.run_validation()
    
    if success:
        print("\n✅ System validation completed successfully!")
        print("🚀 Your Financial AI Assistant is ready to run!")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and add your API keys")
        print("2. Run: python main.py")
        print("3. Access the UI at: http://localhost:8501")
        sys.exit(0)
    else:
        print("\n❌ System validation failed!")
        print("Please fix the issues above before running the system.")
        sys.exit(1)

if __name__ == "__main__":
    main()
